<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.projectkorra</groupId>
  <artifactId>projectkorra</artifactId>
  <version>1.9.3-build.7</version>
  <name>ProjectKorra</name>
  <repositories>
    <!-- local jar files, add more using: mvn install:install-file -Dfile=aaa.jar -DgroupId=aaa -DartifactId=aaa -Dversion=aaa -Dpackaging=jar -DlocalRepositoryPath=path/to/ProjectKorra/localrepo/ -->
    <repository>
      <id>project.local</id>
      <name>project</name>
      <url>file://${project.basedir}/localrepo/</url>
    </repository>
    <!-- WorldGuard and WorldEdit Repo -->
    <repository>
      <id>enginehub-repo</id>
      <url>https://maven.enginehub.org/repo/</url>
    </repository>
    <!-- GriefPrevention Repo -->
    <repository>
      <id>jitpack.io</id>
      <url>https://jitpack.io</url>
    </repository>
    <!-- NoCheat Repo -->
    <repository>
      <id>md_5-snapshots</id>
      <url>https://repo.md-5.net/content/repositories/snapshots/</url>
    </repository>
    <!-- Spigot Repo -->
    <repository>
      <id>spigot-repo</id>
      <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
    </repository>
    <!-- Aikars Repo -->
    <repository>
      <id>aikar</id>
      <url>https://repo.aikar.co/nexus/content/groups/aikar/</url>
    </repository>
    <!-- Paper Repo -->
    <repository>
      <id>papermc</id>
      <url>https://repo.papermc.io/repository/maven-public/</url>
    </repository>
    <!-- Placeholder API Repo -->
    <repository>
      <id>placeholderapi</id>
      <url>https://repo.extendedclip.com/releases/</url>
    </repository>
  </repositories>
  <dependencies>
    <!-- PaperLib -->
    <dependency>
      <groupId>io.papermc</groupId>
      <artifactId>paperlib</artifactId>
      <version>1.0.1</version>
    </dependency>
    <!-- Paper API -->
    <dependency>
      <groupId>io.papermc.paper</groupId>
      <artifactId>paper-api</artifactId>
      <version>1.21.4-R0.1-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    <!-- lang3 -->
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.8.1</version>
    </dependency>
    <!-- Factions -->
    <dependency>
      <groupId>me.markeh</groupId>
      <artifactId>factionsframework</artifactId>
      <version>1.2.0</version>
      <scope>provided</scope>
  </dependency>
    <!-- LWC -->
    <!-- NoCheatPlus -->
    <dependency>
      <groupId>fr.neatmonster</groupId>
      <artifactId>nocheatplus</artifactId>
      <version>3.16.1-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    <!-- PreciousStones -->
    <!-- outdated
<dependency>
  <groupId>net.sacredlabyrinth.Phaed</groupId>
  <artifactId>PreciousStones</artifactId>
  <version>LATEST</version>
  <scope>system</scope>
  <systemPath>${project.basedir}/lib/PreciousStones 10.6.1.jar</systemPath>
</dependency>
-->
    <!-- Residence -->
    <dependency>
      <groupId>com.bekvon.bukkit</groupId>
      <artifactId>residence</artifactId>
      <version>4.8.3.1</version>
      <scope>provided</scope>
    </dependency>
    <!-- Towny -->
    <dependency>
      <groupId>com.palmergames</groupId>
      <artifactId>Towny</artifactId>
      <version>0.93.0.0</version>
      <scope>provided</scope>
    </dependency>
    <!-- WorldEdit / WorldGuard -->
    <dependency>
      <groupId>com.sk89q.worldedit</groupId>
      <artifactId>worldedit-core</artifactId>
      <version>7.0.0-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.sk89q.worldguard</groupId>
      <artifactId>worldguard-core</artifactId>
      <version>7.0.0-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.sk89q.worldedit</groupId>
      <artifactId>worldedit-bukkit</artifactId>
      <version>7.0.0-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.sk89q.worldguard</groupId>
      <artifactId>worldguard-legacy</artifactId>
      <version>7.0.0-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    <!-- RedProtect -->
    <dependency>
      <groupId>br.net.fabiozumbi12</groupId>
      <artifactId>RedProtect</artifactId>
      <version>7.5.5</version>
      <scope>provided</scope>
    </dependency>
    <!-- Kingdoms -->
    <dependency>
      <groupId>com.github.cryptomorin</groupId>
      <artifactId>kingdoms</artifactId>
      <version>1.17.1</version>
      <scope>system</scope>
      <systemPath>${project.basedir}/libs/KingdomsX-1.17.11-BETA.jar</systemPath>
	</dependency>
    <!-- PlaceholderAPI -->
    <dependency>
      <groupId>me.clip</groupId>
      <artifactId>placeholderapi</artifactId>
      <version>2.11.6</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.github.ben-manes.caffeine</groupId>
      <artifactId>caffeine</artifactId>
      <version>3.1.6</version>
    </dependency>
  </dependencies>
  <build>
    <defaultGoal>clean package install</defaultGoal>
    <finalName>${project.name}-${version}</finalName>
    <sourceDirectory>${project.basedir}/src/</sourceDirectory>
    <resources>
      <resource>
        <targetPath>.</targetPath>
        <filtering>true</filtering>
        <directory>${project.basedir}/src/</directory>
        <includes>
          <include>*.yml</include>
        </includes>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.0</version>
        <configuration>
          <source>17</source>
          <target>17</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>3.1.1</version>
        <configuration>
          <outputDirectory>${dir}</outputDirectory>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>3.3.0</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <phase>verify</phase>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.3.0</version>
        <configuration>
          <dependencyReducedPomLocation>${project.build.directory}/dependency-reduced-pom.xml</dependencyReducedPomLocation>
          <relocations>
            <relocation>
              <pattern>io.papermc.lib</pattern>
              <shadedPattern>projectkorra.paperlib</shadedPattern>
            </relocation>
            <relocation>
              <pattern>org.apache.commons</pattern>
              <shadedPattern>projectkorra.commonslang3</shadedPattern>
            </relocation>
            <relocation>
              <pattern>com.github.benmanes.caffeine</pattern>
              <shadedPattern>projectkorra.caffeine</shadedPattern>
            </relocation>
          </relocations>
        </configuration>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <minimizeJar>true</minimizeJar>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <properties>
    <dir>${project.basedir}/target/</dir>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
  <distributionManagement>
    <repository>
      <id>tlb-repo</id>
      <url>http://repo.thelastblockbender.com/private</url>
    </repository>
  </distributionManagement>
</project>
