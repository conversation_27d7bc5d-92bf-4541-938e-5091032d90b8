name: ProjectKorra
author: ProjectKorra
api-version: 1.16
version: ${project.version}
main: com.projectkorra.projectkorra.ProjectKorra
softdepend: [PreciousStones, WorldGuard, WorldEdit, FactionsFramework, GriefPrevention, Towny, NoCheatPlus, LWC, Residence, RedProtect, PlaceholderAPI]
commands:
  project<PERSON>ra:
    aliases: [b,bending,mtla,tla,korra,pk,bend]
    usage: /<command>
permissions:
  bending.admin:
    default: op
    description: <PERSON>s access to all commands and abilities.
    children:
      bending.player: true
      bending.command.reload: true
      bending.admin.permaremove: true
      bending.command.add.others: true
      bending.command.add: true
      bending.command.rechoose: true
      bending.admin.choose: true
      bending.choose.ignorecooldown: true
      bending.ability.AvatarState: true
      bending.water.bloodbending.anytime: true
      bending.water.bloodbending: true
      bending.ability.Flight: true
      bending.ability.MetalClips.loot: true
      bending.ability.MetalClips.4clips: true
      bending.ability.MetalClips.throw: true
      bending.command.toggle.all: true
      bending.admin.toggle: true
      bending.command.invincible: true
      bending.command.check: true
      bending.command.preset.bind.assign: true
      bending.command.preset.bind.external: true
      bending.command.preset.bind.external.other: true
      bending.command.copy.assign: true
      bending.admin.debug: true
      bending.admin.remove: true
  bending.player:
    default: true
    description: Grants access to most abilities and basic commands.
    children:
      bending.command.bind: true
      bending.command.board: true
      bending.command.display: true
      bending.command.toggle: true
      bending.command.copy: true
      bending.command.choose: true
      bending.command.version: true
      bending.command.help: true
      bending.command.clear: true
      bending.command.who: true
      bending.command.preset.list: true
      bending.command.preset.create.5: true
      bending.command.preset.create: true
      bending.command.preset.bind: true
      bending.command.preset.delete: true
      bending.air: true
      bending.water: true
      bending.earth: true
      bending.fire: true
      bending.chi: true
  bending.air:
    default: true
    description: Grants access to all airbending abilities.
    children:
      bending.command.add.air: true
      bending.command.choose.air: true
      bending.ability.AirBlast: true
      bending.ability.AirBurst: true
      bending.ability.AirScooter: true
      bending.ability.AirShield: true
      bending.ability.AirSpout: true
      bending.ability.AirSuction: true
      bending.ability.AirSwipe: true
      bending.ability.Suffocate: true
      bending.ability.Tornado: true
      bending.ability.AirCombo: true
      bending.air.passive: true
      bending.air.flight: true
  bending.water:
    default: true
    description: Grants access to most waterbending abilities.
    children:
      bending.command.add.water: true
      bending.command.choose.water: true
      bending.ability.Bloodbending: true
      bending.ability.HealingWaters: true
      bending.ability.IceBlast: true
      bending.ability.IceSpike: true
      bending.ability.OctopusForm: true
      bending.ability.PhaseChange: true
      bending.ability.Surge: true
      bending.ability.Torrent: true
      bending.ability.WaterBubble: true
      bending.ability.WaterManipulation: true
      bending.ability.WaterSpout: true
      bending.ability.WaterSpout.Wave: true
      bending.ability.WaterCombo: true
      bending.water.plantbending: true
      bending.message.nightmessage: true
      bending.water.passive: true
      bending.water.icebending: true
      bending.water.healing: true
      bending.ability.WaterArms: true
      bending.ability.WaterArms.Pull: true
      bending.ability.WaterArms.Punch: true
      bending.ability.WaterArms.Grapple: true
      bending.ability.WaterArms.Grab: true
      bending.ability.WaterArms.Freeze: true
      bending.ability.WaterArms.Spear: true
      bending.ability.Bottlebending: true
  bending.earth:
    default: true
    description: Grants access to all Earthbending abilities.
    children:
      bending.command.add.earth: true
      bending.command.choose.earth: true
      bending.ability.Catapult: true
      bending.ability.Collapse: true
      bending.ability.EarthArmor: true
      bending.ability.EarthBlast: true
      bending.ability.EarthGrab: true
      bending.ability.EarthTunnel: true
      bending.ability.RaiseEarth: true
      bending.ability.Shockwave: true
      bending.ability.Tremorsense: true
      bending.ability.Extraction: true
      bending.ability.MetalClips: true
      bending.earth.passive: true
      bending.earth.metalbending: true
      bending.earth.lavabending: true
      bending.earth.sandbending: true
      bending.ability.LavaFlow: true
      bending.ability.EarthSmash: true
      bending.ability.SandSpout: true
      bending.ability.EarthCombo: true
  bending.fire:
    default: true
    description: Grants access to all firebending abilities.
    children:
      bending.command.add.fire: true
      bending.command.choose.fire: true
      bending.ability.Blaze: true
      bending.ability.FireBlast: true
      bending.ability.FireBurst: true
      bending.ability.FireJet: true
      bending.ability.FireShield: true
      bending.ability.HeatControl: true
      bending.ability.Illumination: true
      bending.ability.Lightning: true
      bending.ability.WallOfFire: true
      bending.ability.Combustion: true
      bending.ability.FireManipulation: true
      bending.ability.FireCombo: true
      bending.message.daymessage: true
      bending.fire.passive: true
      bending.fire.lightningbending: true
      bending.fire.combustionbending: true
  bending.chi:
    default: true
    description: Grants access to all ChiBlocking abilities.
    children:
      bending.command.add.chi: true
      bending.command.choose.chi: true
      bending.ability.HighJump: true
      bending.ability.Paralyze: true
      bending.ability.RapidPunch: true
      bending.ability.Smokescreen: true
      bending.ability.WarriorStance: true
      bending.ability.AcrobatStance: true
      bending.ability.QuickStrike: true
      bending.ability.SwiftKick: true
      bending.ability.ChiCombo: true
      bending.chi.passive: true
  bending.avatar:
    default: false
    description: Grants the Avatar Color.
  bending.ability.MetalClips.loot:
    default: false
    description: Lets a Metalbender loot a player's inventory of its iron.
  bending.ability.MetalClips.throw:
    default: false
    description: Lets a Metalbending throw a controlled entity.
  bending.ability.AirCombo:
    default: false
    description: Grants access to all AirCombos.
    children:
      bending.ability.AirSweep: true
      bending.ability.AirStream: true
      bending.ability.Twister: true
  bending.ability.Flight:
    default: false
    description: Grants access to Flight sub-abilities.
    children:
      bending.ability.Flight.Soar: true
      bending.ability.Flight.Glide: true
      bending.ability.Flight.Levitate: true
      bending.ability.Flight.Ending: true
  bending.ability.WaterCombo:
    default: false
    description: Grants access to all WaterCombos.
    children:
      bending.ability.IceWave: true
      bending.ability.IceBullet: true
      bending.ability.IceBulletLeftClick: true
      bending.ability.IceBulletRightClick: true
  bending.ability.EarthCombo:
    default: false
    description: Grants access to all EarthCombos.
    children:
      bending.ability.EarthPillars: true
      bending.ability.EarthDome: true
      bending.ability.EarthDomeOthers: true
  bending.ability.FireCombo:
    default: false
    description: Grants access to all FireCombos.
    children:
      bending.ability.FireKick: true
      bending.ability.FireSpin: true
      bending.ability.JetBlast: true
      bending.ability.JetBlaze: true
      bending.ability.FireWheel: true
  bending.ability.ChiCombo:
    default: false
    description: Grants access to all ChiCombos.
    children:
      bending.ability.Immobilize: true
  bending.air.passive:
    default:  false
    description: Grants access to all air passives.
    children:
      bending.ability.AirAgility: true
      bending.ability.AirSaturation: true
      bending.ability.GracefulDescent: true
  bending.chi.passive:
    default:  false
    description: Grants access to all chi passives.
    children:
      bending.ability.Acrobatics: true
      bending.ability.ChiAgility: true
      bending.ability.ChiSaturation: true
  bending.earth.passive:
    default:  false
    description: Grants access to all earth passives.
    children:
      bending.ability.DensityShift: true
      bending.ability.SandAgility: true
  bending.fire.passive:
    default:  false
    description: Grants access to all fire passives.
    children:
  bending.water.passive:
    default:  false
    description: Grants access to all water passives.
    children:
      bending.ability.FastSwim: true
      bending.ability.HydroSink: true
  bending.donor:
    default: false
    description: Grants the Donor tag.
