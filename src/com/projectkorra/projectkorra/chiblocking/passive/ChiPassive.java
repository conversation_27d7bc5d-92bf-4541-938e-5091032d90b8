package com.projectkorra.projectkorra.chiblocking.passive;

import com.projectkorra.projectkorra.event.BendingRestrictEvent;
import org.bukkit.Bukkit;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.Element;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.ability.ChiAbility;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.airbending.Suffocate;
import com.projectkorra.projectkorra.chiblocking.AcrobatStance;
import com.projectkorra.projectkorra.chiblocking.QuickStrike;
import com.projectkorra.projectkorra.chiblocking.SwiftKick;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ActionBar;

public class ChiPassive {
	public static boolean willChiBlock(final Player attacker, final Player player) {
		final BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
		if (bPlayer == null) {
			return false;
		}

		final ChiAbility stance = bPlayer.getStance();
		final QuickStrike quickStrike = CoreAbility.getAbility(player, QuickStrike.class);
		final SwiftKick swiftKick = CoreAbility.getAbility(player, SwiftKick.class);
		double newChance = getChance();

		if (stance != null && stance instanceof AcrobatStance) {
			newChance += ((AcrobatStance) stance).getChiBlockBoost();
		}

		if (quickStrike != null) {
			newChance += quickStrike.getBlockChance();
		} else if (swiftKick != null) {
			newChance += swiftKick.getBlockChance();
		}

		if (Math.random() > newChance / 100.0) {
			return false;
		} else if (bPlayer.isChiBlocked()) {
			return false;
		}

		return true;
	}

	public static void blockChi(final Player player) {
		blockChi(null, player);
	}

	public static void blockChi(final Player source, final Player player) {
		blockChi(source, player, getDuration());
	}

	public static void blockChi(final Player source, final Player player, final long duration) {
		blockChi(source, player, duration, false);
	}

	public static void blockChi(final Player source, final Player player, final long duration, final boolean isSilent) {
		var event = new BendingRestrictEvent(source, player);
		Bukkit.getPluginManager().callEvent(event);
		if (event.isCancelled()) {
			return;
		}
		if (Suffocate.isChannelingSphere(player)) {
			Suffocate.remove(player);
		}

		final BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
		if (bPlayer == null) {
			return;
		}

		bPlayer.blockChi();
		if (!isSilent) {
			player.getWorld().playSound(player.getLocation(), Sound.ENTITY_ENDER_DRAGON_HURT, 2, 0);
		}

		final long endTime = System.currentTimeMillis() + duration;
		new BukkitRunnable() {
			@Override
			public void run() {
				if (!isSilent) {
					ActionBar.sendActionBar(Element.CHI.getColor() + "* Chiblocked *", player);
				}
				if (System.currentTimeMillis() >= endTime) {
					bPlayer.unblockChi();
					this.cancel();
				}
			}
		}.runTaskTimer(ProjectKorra.plugin, 0, 1);
	}

	public static double getChance() {
		return ConfigManager.getConfig().getDouble("Abilities.Chi.Passive.BlockChi.Chance");
	}

	public static int getDuration() {
		return ConfigManager.getConfig().getInt("Abilities.Chi.Passive.BlockChi.Duration");
	}

	public static long getTicks() {
		return (getDuration() / 1000) * 20;
	}
}
