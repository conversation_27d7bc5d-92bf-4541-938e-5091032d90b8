package com.projectkorra.projectkorra.chiblocking;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.ChiAbility;
import com.projectkorra.projectkorra.command.Commands;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import org.bukkit.Location;
import org.bukkit.entity.Egg;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

import java.util.Optional;
import java.util.concurrent.ThreadLocalRandom;

public class PoisonBomb extends ChiAbility {
    public static final String POISON_BOMB = "pk-poison-bomb";

    private int duration;
    private long cooldown;
    private double radius;

    public PoisonBomb(Player player) {
        super(player);
        if (!bPlayer.canBend(this)) {
            return;
        }

        int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
        long currentLevel = TLBMethods.limitLevels(player, statLevel);

        this.cooldown = TLBMethods.getLong("Abilities.Chi.PoisonBomb.Cooldown", currentLevel);
        this.duration = TLBMethods.getInt("Abilities.Chi.PoisonBomb.Duration", currentLevel);
        this.radius = TLBMethods.getDouble("Abilities.Chi.PoisonBomb.Radius", currentLevel);
        start();
    }

    private void playEffect(Location loc) {
		ThreadLocalRandom rand = ThreadLocalRandom.current();
		for (int i = 0; i < 75; i++) {
			ParticleEffect.SMOKE_NORMAL.display(loc, 5, rand.nextDouble(1.7), rand.nextDouble(1.5), rand.nextDouble(1.7));
		}
    }

    @Override
    public void progress() {
        Egg egg = player.launchProjectile(Egg.class);
        egg.setMetadata(POISON_BOMB, new FixedMetadataValue(ProjectKorra.plugin, this));
        this.bPlayer.addCooldown(this);
        remove();
    }

    public static Optional<PoisonBomb> getInstance(Entity entity) {
        if (entity.hasMetadata(POISON_BOMB)) {
            return Optional.ofNullable((PoisonBomb) entity.getMetadata(POISON_BOMB).get(0).value());
        }
        return Optional.empty();
    }

    public void applyPoisonBomb(Location center) {
        if (GeneralMethods.isRegionProtectedFromBuild(this, center)) {
            return;
        }
        playEffect(center);
        for (Entity en : GeneralMethods.getEntitiesAroundPoint(center, radius)) {
            if (en instanceof LivingEntity livingEntity) {
                if (Commands.invincible.contains(livingEntity.getName())) {
                    return;
                }
                livingEntity.addPotionEffect(new PotionEffect(PotionEffectType.POISON, duration, 1));
            }
        }
    }

    @Override
    public String getName() {
        return "PoisonBomb";
    }

    @Override
    public Location getLocation() {
        return player != null ? player.getLocation() : null;
    }

    @Override
    public long getCooldown() {
        return cooldown;
    }

    @Override
    public boolean isSneakAbility() {
        return false;
    }

    @Override
    public boolean isHarmlessAbility() {
        return false;
    }

    public double getRadius() {
        return radius;
    }
}
