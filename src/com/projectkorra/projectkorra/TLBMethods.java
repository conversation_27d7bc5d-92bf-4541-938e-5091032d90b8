package com.projectkorra.projectkorra;

import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.StatisticsManager;
import com.sk89q.worldedit.bukkit.BukkitAdapter;
import com.sk89q.worldedit.util.Location;
import com.sk89q.worldguard.LocalPlayer;
import com.sk89q.worldguard.WorldGuard;
import com.sk89q.worldguard.bukkit.WorldGuardPlugin;
import com.sk89q.worldguard.protection.ApplicableRegionSet;
import com.sk89q.worldguard.protection.flags.DoubleFlag;
import com.sk89q.worldguard.protection.regions.RegionContainer;
import com.sk89q.worldguard.protection.regions.RegionQuery;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.util.NumberConversions;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

public final class TLBMethods {
	private static final double MAX_LEVEL_DIFF = 9; // Usual max level = 10, starting level = 1, diff 10-1 = 9
	private static final Set<String> EQUALIZED_WORLDS = new HashSet<>();
	private static final Map<String, Long> CAPPED_WORLDS = new HashMap<>();
	private static final Map<UUID, Long> handicapped = new HashMap<>();
	private static final Map<String, Integer> bonusUses = new HashMap<>();

	private static boolean useBonus = false;
	private static long equalizedLevel;
	private static boolean mutiplierEnabled = ConfigManager.defaultConfig.get().getBoolean("Properties.MultiplyLevels");

	public static void initEqualizedWorlds() {
		equalizedLevel = ConfigManager.defaultConfig.get().getLong("Properties.EqualizedWorldsLevel", 5);
		 
		loadBonuses();

		EQUALIZED_WORLDS.clear();
		EQUALIZED_WORLDS.addAll(ConfigManager.defaultConfig.get().getStringList("Properties.EqualizedWorlds"));

		CAPPED_WORLDS.clear();
		ConfigurationSection section = ConfigManager.defaultConfig.get().getConfigurationSection("Properties.CappedWorlds");
		if (section != null) {
			section.getKeys(false).forEach(k -> CAPPED_WORLDS.put(k, section.getLong(k, 5)));
		}
	}
	
	//Load and apply levelMultipliers
	public static double multiplyLevel(Player player) {
	   	
		final BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player.getName());
		
	    if (bPlayer.hasElement(Element.EARTH)) {
	    	return ConfigManager.defaultConfig.get().getDouble("Properties.LevelMultipliers.Earth");
	    }
	    else if (bPlayer.hasElement(Element.FIRE)) {
	    	return ConfigManager.defaultConfig.get().getDouble("Properties.LevelMultipliers.Fire");
	    }
	    else if (bPlayer.hasElement(Element.WATER)) {
	    	return ConfigManager.defaultConfig.get().getDouble("Properties.LevelMultipliers.Water");
	    }
	    else if (bPlayer.hasElement(Element.AIR)) {
	    	return ConfigManager.defaultConfig.get().getDouble("Properties.LevelMultipliers.Air");
	    }
	    else if (bPlayer.hasElement(Element.CHI)) {
	    	return ConfigManager.defaultConfig.get().getDouble("Properties.LevelMultipliers.Chi");
	    }
	    else {
	    	return 1;
	    }
	}
	    
  public static void loadBonuses() {
    bonusUses.clear();
    ConfigurationSection sectionBonus = ConfigManager.defaultConfig.get().getConfigurationSection("Properties.BonusUses");
    if (sectionBonus != null) {
      sectionBonus.getKeys(false).forEach(k -> {
        if (Element.getElement(k) != null || CoreAbility.getAbility(k) != null) {
          String key = k.toLowerCase(Locale.ROOT);
          int value = Math.max(1, sectionBonus.getInt(k, 1));
          if (value > 1) {
            ProjectKorra.log.info(key + " levelling bonus set to " + value);
            bonusUses.put(key, value);
          }
        }
      });
    }
  }

	/**
	 * Set ability use bonus
	 */
	public static void setBonus(boolean enabled) {
		useBonus = enabled;
	}

	public static boolean isXPBonusEnabled() {
		return useBonus;
	}

  public static int usesAfterBonus(CoreAbility ability) {
    String element = ability.getElement().getName().toLowerCase(Locale.ROOT);
    String name = ability.getName().toLowerCase(Locale.ROOT);
    int uses = Math.max(bonusUses.getOrDefault(element, 1), bonusUses.getOrDefault(name, 1));
    return useBonus ? uses * 2 : uses;
  }

	/**
	 * Enable xp bonus for the specified duration in seconds
	 */
	public static void runBonus(long duration) {
		setBonus(true);
		Bukkit.getScheduler().scheduleSyncDelayedTask(ProjectKorra.plugin, () -> setBonus(false), 20 * duration);
	}

	/**
	 * Checks if user has a handicap applied
	 *
	 * @param player the player to check
	 * @return true if player is handicapped
	 */
	public static boolean isHandicapped(Player player) {
		return handicapped.containsKey(player.getUniqueId());
	}

	/**
	 * Enforces level limit
	 *
	 * @param player the player to limit
	 * @param statLevel the statistic to check
	 * @return the limited level
	 */
	public static long limitLevels(Player player, int statLevel) {
		if (EQUALIZED_WORLDS.contains(player.getWorld().getName())) {
			return equalizedLevel;
		}
		
		final DoubleFlag bendingLevelFlag = (DoubleFlag) WorldGuard.getInstance().getFlagRegistry().get("bending-level");
		
		if (bendingLevelFlag != null) {
		
			Location loc = BukkitAdapter.adapt(player.getLocation());
			RegionContainer container = WorldGuard.getInstance().getPlatform().getRegionContainer();
			RegionQuery query = container.createQuery();
			ApplicableRegionSet set = query.getApplicableRegions(loc);
			
			LocalPlayer localPlayer = WorldGuardPlugin.inst().wrapPlayer(player);
			Double regionLevel = set.queryValue(localPlayer, bendingLevelFlag);
			
			if (regionLevel != null) {
				return regionLevel.longValue();
			}
		}
		
		long level = Manager.getManager(StatisticsManager.class).getStatisticCurrent(player.getUniqueId() , statLevel);
		if (isHandicapped(player)) {
			return Math.min(level, handicapped.get(player.getUniqueId()));
		}

		
		if (mutiplierEnabled) {
			return (long) (level * multiplyLevel(player));
		}
		
		long levelCap = CAPPED_WORLDS.getOrDefault(player.getWorld().getName(), level);
		return Math.min(level, levelCap);
	}

	/**
	 * Applies a level handicap
	 *
	 * @param player the player to handicapped
	 * @param level the level handicapped to
	 */
	public static void applyHandicap(Player player, long level) {
			handicapped.put(player.getUniqueId(), level);
	}

	/**
	 * Removes a level handicap
	 *
	 * @param player the player to handicapped
	 */
	public static void removeHandicap(Player player) {
	  handicapped.remove(player.getUniqueId());
	}

	/**
	 * Get's the current handicap level for the specified player
	 * @param player the player to check
	 * @return the handicap level or -1 if not found
	 */
	public static long getHandicapLevel(Player player) {
		return handicapped.getOrDefault(player.getUniqueId(), -1L);
	}

	// Formatting

	public static String getTLBHeader() {
		return ChatColor.DARK_PURPLE + "============= " + ChatColor.AQUA + "THE LAST BLOCKBENDER" + ChatColor.DARK_PURPLE + " ============";
	}

	public static String getTLBFooter() {
		return ChatColor.DARK_PURPLE + "==============================================";
	}

	public static ChatColor getElementColor(Element element) {
		switch (element.getName()) { // smh, element should be an enum
			case "Water":
			case "Healing":
				return ChatColor.AQUA;
			case "Fire":
				return ChatColor.RED;
			case "Earth":
				return ChatColor.GREEN;
			case "Lightning":
			case "Sand":
				return ChatColor.YELLOW;
			case "Combustion":
				return ChatColor.DARK_RED;
			case "Metal":
				return ChatColor.DARK_GRAY;
			case "Lava":
				return ChatColor.DARK_GREEN;
			case "Ice":
			case "Plant":
				return ChatColor.DARK_AQUA;
			case "Blood":
				return ChatColor.BLUE;
			case "Chi":
				return ChatColor.GOLD;
			case "Flight":
			case "Spiritual":
				return ChatColor.GRAY;
			case "Air":
			default:
				return ChatColor.WHITE;
		}
	}

	public static int getInt(String configKey, long level) {
		ConfigurationSection section = ConfigManager.scalingConfig.get().getConfigurationSection(configKey);
		int def = ConfigManager.defaultConfig.get().getInt(configKey);
		if (section == null) return def;

		if (section.contains("Level"+level)) return section.getInt("Level"+level);

		int min = section.getInt("MinLevel", def);
		int max = section.getInt("MaxLevel", def);

		if (min == max) return min;

		double factor = (level - 1) / MAX_LEVEL_DIFF;
		return min + NumberConversions.ceil((max - min) * factor);
	}

	public static long getLong(String configKey, long level) {
		ConfigurationSection section = ConfigManager.scalingConfig.get().getConfigurationSection(configKey);
		long def = ConfigManager.defaultConfig.get().getLong(configKey);
		if (section == null) return def;

		if (section.contains("Level"+level)) return section.getLong("Level"+level);

		long min = section.getLong("MinLevel", def);
		long max = section.getLong("MaxLevel", def);

		if (min == max) return min;

		double factor = (level - 1) / MAX_LEVEL_DIFF;
		return min + (long) ((max - min) * factor);
	}

	public static double getDouble(String configKey, long level) {
		ConfigurationSection section = ConfigManager.scalingConfig.get().getConfigurationSection(configKey);
		double def = ConfigManager.defaultConfig.get().getDouble(configKey);
		if (section == null) return def;

		if (section.contains("Level"+level)) return section.getDouble("Level"+level);

		double min = section.getDouble("MinLevel", def);
		double max = section.getDouble("MaxLevel", def);

		if (min == max) return min;

		double factor = (level - 1) / MAX_LEVEL_DIFF;
		return min + (max - min) * factor;
	}
}
