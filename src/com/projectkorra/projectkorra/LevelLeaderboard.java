package com.projectkorra.projectkorra;

import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.storage.DBConnection;
import com.projectkorra.projectkorra.util.Statistic;
import com.projectkorra.projectkorra.util.StatisticsManager;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.minimessage.MiniMessage;
import net.kyori.adventure.text.minimessage.tag.resolver.Placeholder;
import net.kyori.adventure.text.minimessage.tag.resolver.TagResolver;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class LevelLeaderboard {
    private final int limit;
    private final MiniMessage MINI = MiniMessage.miniMessage();
    private final LegacyComponentSerializer LEGACY = LegacyComponentSerializer.legacyAmpersand().toBuilder().hexColors().build();

    private final int totalLevelStatID;
    private List<LeaderboardEntry> entries = List.of();
    private final Set<UUID> blacklist = ConcurrentHashMap.newKeySet();

    public LevelLeaderboard() {
        totalLevelStatID = StatisticsMethods.getId(Statistic.TOTAL_LEVEL.getName());
        limit = Math.max(10, ConfigManager.getConfig().getInt("Properties.Leaderboard.Limit", 100));
        long minutes = Math.max(1, ConfigManager.getConfig().getLong("Properties.Leaderboard.IntervalMinutes", 15));
        setupBlacklist();
        ProjectKorra.plugin.getSLF4JLogger().info("Level Leaderboard (Top " + limit + ") updating every " + minutes + " minutes!");
        Bukkit.getScheduler().runTaskTimerAsynchronously(ProjectKorra.plugin, this::update, 1, 20 * 60 * minutes);
    }

    private void setupBlacklist() {
        blacklist.clear();
        ConfigManager.getConfig().getStringList("Properties.Leaderboard.BlackList").stream().map(s -> {
            try {
                return UUID.fromString(s);
            } catch (Exception ignore) {
            }
            return null;
        }).filter(Objects::nonNull).forEach(blacklist::add);
    }

    public void reload() {
        setupBlacklist();
        Bukkit.getScheduler().runTaskAsynchronously(ProjectKorra.plugin, this::update);
    }

    public LeaderboardEntry get(int index) {
        if (index < 1 || index > entries.size()) {
            return null;
        }
        return entries.get(index - 1);
    }

    public void update() {
        // Get top levels from database
        String query = "SELECT uuid, statValue FROM pk_stats WHERE statId = ? ORDER BY statValue DESC LIMIT " + limit;
        ResultSet rs;
        List<LeaderboardEntry> dbResults = new ArrayList<>(limit);
        var banned = Bukkit.getBannedPlayers().stream()
          .map(OfflinePlayer::getUniqueId)
          .collect(Collectors.toUnmodifiableSet());
        long minValue = 0;
        try (PreparedStatement ps = DBConnection.sql.getConnection().prepareStatement(query)) {
            ps.setInt(1, totalLevelStatID);
            rs = ps.executeQuery();
            while (rs.next()) {
                UUID uuid = UUID.fromString(rs.getString("uuid"));
                if (!banned.contains(uuid)) {
                    int level = rs.getInt("statValue");
                    if (level > minValue) {
                        minValue = level;
                    }
                    dbResults.add(new LeaderboardEntry(uuid, level));
                }
            }
        } catch (SQLException e) {
            ProjectKorra.plugin.getSLF4JLogger().warn(e.getMessage(), e);
            return;
        }
        // Get any levels from online players that haven't been written to database
        Map<UUID, Long> collected = Manager.getManager(StatisticsManager.class).collectLevels(totalLevelStatID, minValue);
        // Switch to main thread for this
        Bukkit.getScheduler().runTaskLater(ProjectKorra.plugin, () -> {
            // Modify list if needed
            ListIterator<LeaderboardEntry> it = dbResults.listIterator();
            while (it.hasNext()) {
                LeaderboardEntry entry = it.next();
                Long result = collected.get(entry.uuid);
                if (result != null && result > entry.level()) {
                    it.set(new LeaderboardEntry(entry.uuid, result.intValue()));
                }
            }
            // Sort the modified list
            Collections.sort(dbResults);
            entries = List.copyOf(dbResults);
        }, 1);
    }

    public class LeaderboardEntry implements Comparable<LeaderboardEntry> {
        public static final Comparator<LeaderboardEntry> COMPARATOR = Comparator.comparingInt(LeaderboardEntry::level).reversed();

        private final UUID uuid;
        private final int level;
        private CachedFormat cachedFormat;

        public LeaderboardEntry(UUID uuid, int level) {
            this.uuid = uuid;
            this.level = level;
        }

        private Component name() {
            String name = Bukkit.getOfflinePlayer(uuid).getName();
            return name == null ? Component.empty() : Component.text(name);
        }

        public UUID uuid() {
            return uuid;
        }

        public int level() {
            return level;
        }

        public CachedFormat format() {
            if (cachedFormat == null) {
                String format = ConfigManager.languageConfig.get().getString("Placeholder.LevelFormat", "<name> - <level>");
                var builder = TagResolver.builder()
                        .resolver(Placeholder.component("uuid", Component.text(uuid.toString())))
                        .resolver(Placeholder.component("name", name()))
                        .resolver(Placeholder.component("level", Component.text(level)));
                Component component = MINI.deserialize(format, builder.build());
                String legacy = LEGACY.serialize(component);
                cachedFormat = new CachedFormat(component, legacy);
            }
            return cachedFormat;
        }

        @Override
        public int compareTo(LeaderboardEntry o) {
            return COMPARATOR.compare(this, o);
        }
    }

    public record CachedFormat(Component component, String legacy) {
    }
}
