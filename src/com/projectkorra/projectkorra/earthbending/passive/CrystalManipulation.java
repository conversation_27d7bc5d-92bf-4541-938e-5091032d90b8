package com.projectkorra.projectkorra.earthbending.passive;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.ability.EarthAbility;
import com.projectkorra.projectkorra.ability.PassiveAbility;
import com.projectkorra.projectkorra.attribute.Attribute;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.SoundCategory;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.block.data.BlockData;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.PlayerInventory;
import org.bukkit.util.Vector;

import static java.util.Map.entry;

public class CrystalManipulation extends EarthAbility implements PassiveAbility {
  private static final ItemStack AIR = new ItemStack(Material.AIR);
  private static final Map<Material, Food> EDIBLES;
  private static final Map<Material, ItemStack> DROPS;

  static {
    EDIBLES = Map.ofEntries(
      entry(Material.AMETHYST_SHARD, new Food(2, 0.2F)),
      entry(Material.SMALL_AMETHYST_BUD, new Food(2, 0.2F)),
      entry(Material.MEDIUM_AMETHYST_BUD, new Food(2, 0.4F)),
      entry(Material.LARGE_AMETHYST_BUD, new Food(3, 0.4F)),
      entry(Material.AMETHYST_CLUSTER, new Food(4, 0.4F))
    );
    DROPS = Map.ofEntries(
      entry(Material.SMALL_AMETHYST_BUD, shards(1)),
      entry(Material.MEDIUM_AMETHYST_BUD, shards(1)),
      entry(Material.LARGE_AMETHYST_BUD, shards(1)),
      entry(Material.AMETHYST_CLUSTER, shards(2)),
      entry(Material.AMETHYST_BLOCK, shards(2)),
      entry(Material.BUDDING_AMETHYST, shards(1))
    );
  }

  private static ItemStack shards(int amount) {
    return new ItemStack(Material.AMETHYST_SHARD, amount);
  }

  @Attribute(Attribute.COOLDOWN)
  private final long eatCooldown;
  @Attribute(Attribute.COOLDOWN)
  private final long shatterCooldown;
  @Attribute("Yield")
  private final double yield;
  @Attribute(Attribute.SPEED)
  private final double pullSpeed;

  public CrystalManipulation(Player player) {
    super(player);

    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    this.eatCooldown = TLBMethods.getLong("Abilities.Earth.Passive.CrystalManipulation.EatCooldown", currentLevel);
    this.shatterCooldown = TLBMethods.getLong("Abilities.Earth.Passive.CrystalManipulation.ShatterCooldown", currentLevel);
    this.yield = TLBMethods.getDouble("Abilities.Earth.Passive.CrystalManipulation.ShatterYield", currentLevel);
    this.pullSpeed = TLBMethods.getDouble("Abilities.Earth.Passive.CrystalManipulation.PullSpeed", currentLevel);
  }

  @Override
  public void progress() {
  }

  @Override
  public boolean isSneakAbility() {
    return false;
  }

  @Override
  public boolean isHarmlessAbility() {
    return true;
  }

  @Override
  public long getCooldown() {
    return 0;
  }

  @Override
  public String getName() {
    return "CrystalManipulation";
  }

  @Override
  public Location getLocation() {
    return this.player.getLocation();
  }

  @Override
  public boolean isInstantiable() {
    return false;
  }

  @Override
  public boolean isProgressable() {
    return false;
  }

  @Override
  public boolean isHiddenAbility() {
    return true;
  }

  public static boolean tryCrushingCrystals(BendingPlayer player, Collection<Block> blocks) {
    CoreAbility passive = CoreAbility.getAbility(CrystalManipulation.class);
    if (!player.isOnCooldown("CrystalCrush") && player.canBendPassive(passive) && player.canUsePassive(passive)) {
      Map<Block, ItemStack> filtered = new HashMap<>();
      for (Block block : blocks) {
        ItemStack drop = DROPS.get(block.getType());
        if (drop != null && !GeneralMethods.isRegionProtectedFromBuild(player.getPlayer(), block.getLocation())) {
          filtered.put(block, drop);
        }
      }
      return shatter(player, filtered);
    }
    return false;
  }

  private static boolean shatter(BendingPlayer player, Map<Block, ItemStack> map) {
    if (map.isEmpty()) {
      return false;
    }
    CrystalManipulation ability = new CrystalManipulation(player.getPlayer());
    Vector center = player.getPlayer().getLocation().add(0, player.getPlayer().getHeight() / 2, 0).toVector();
    ThreadLocalRandom rand = ThreadLocalRandom.current();
    BlockData data = Material.AMETHYST_BLOCK.createBlockData();
    World world = player.getPlayer().getWorld();
    for (var entry : map.entrySet()) {
      Block block = entry.getKey();
      block.setType(Material.AIR);
      Location loc = block.getLocation().add(0.5, 0.5, 0.5);
      ParticleEffect.BLOCK_CRACK.display(loc, 3, 0.3, 0.3, 0.3, 0, data);
      if (rand.nextBoolean()) {
        float pitch = ThreadLocalRandom.current().nextFloat() * 0.1F + 0.9F;
        world.playSound(loc, data.getSoundGroup().getBreakSound(), 2, pitch);
      }
      if (rand.nextDouble() < ability.yield) {
        Vector velocity = clampVelocity(center.clone().subtract(loc.toVector()).normalize().multiply(ability.pullSpeed));
        world.dropItem(loc, entry.getValue()).setVelocity(velocity);
      }
    }
    player.addCooldown("CrystalCrush", ability.shatterCooldown);
    return true;
  }

  private static final double MIN_VELOCITY_COMPONENT = -4;
  private static final double MAX_VELOCITY_COMPONENT = 4;

  private static Vector clampVelocity(Vector vector) {
    double clampedX = Math.min(MAX_VELOCITY_COMPONENT, Math.max(MIN_VELOCITY_COMPONENT, vector.getX()));
    double clampedY = Math.min(MAX_VELOCITY_COMPONENT, Math.max(MIN_VELOCITY_COMPONENT, vector.getY()));
    double clampedZ = Math.min(MAX_VELOCITY_COMPONENT, Math.max(MIN_VELOCITY_COMPONENT, vector.getZ()));
    return new Vector(clampedX, clampedY, clampedZ);
  }

  public static boolean tryEatCrystal(BendingPlayer player) {
    if (player.getPlayer().getFoodLevel() >= 20) {
      return false;
    }
    CoreAbility passive = CoreAbility.getAbility(CrystalManipulation.class);
    if (!player.isOnCooldown("CrystalMeth") && player.canBendPassive(passive) && player.canUsePassive(passive)) {
      PlayerInventory inv = player.getPlayer().getInventory();
      Food food = consumeCrystalItem(inv, inv.getItemInMainHand());
      if (food == null) {
        food = consumeCrystalItem(inv, inv.getItemInOffHand());
      }
      if (food != null) {
        eatTheDamnCrystal(player.getPlayer(), food);
        player.addCooldown("CrystalMeth", new CrystalManipulation(player.getPlayer()).eatCooldown);
        return true;
      }
    }
    return false;
  }

  private static Food consumeCrystalItem(PlayerInventory inventory, ItemStack item) {
    Food food = EDIBLES.get(item.getType());
    if (food != null) {
      int amount = item.getAmount() - 1;
      if (amount > 0) {
        item.setAmount(amount);
      } else {
        inventory.setItemInMainHand(AIR);
      }
      return food;
    }
    return null;
  }

  private static void eatTheDamnCrystal(Player player, Food food) {
    if (food == null) {
      return;
    }
    int initialFood = player.getFoodLevel();
    int foodLevel = Math.min(initialFood + food.foodLevel(), 20);
    float saturation = player.getSaturation() + food.foodLevel() * food.saturationModifier() * 2;
    float saturationModifier = Math.min(saturation, foodLevel);
    player.setFoodLevel(foodLevel);
    player.setSaturation(saturationModifier);
    float pitch = ThreadLocalRandom.current().nextFloat() * 0.1F + 0.9F;
    player.playSound(player, Sound.ENTITY_PLAYER_BURP, SoundCategory.PLAYERS, 0.5F, pitch);
  }

  private record Food(int foodLevel, float saturationModifier) {
  }
}
