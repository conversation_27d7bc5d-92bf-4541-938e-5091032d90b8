package com.projectkorra.projectkorra.util;

import java.util.Collection;
import java.util.Iterator;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Predicate;

import com.projectkorra.projectkorra.ProjectKorra;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.data.BlockData;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public final class FragileStructure implements Iterable<Block> {
  private final Collection<Block> fragileBlocks;
  private final Predicate<Block> predicate;
  private int health;

  private FragileStructure(Collection<Block> fragileBlocks, Predicate<Block> predicate, int health) {
    this.fragileBlocks = fragileBlocks;
    this.predicate = predicate;
    this.health = health;
    this.fragileBlocks.forEach(b -> b.setMetadata(Metadata.DESTRUCTIBLE, Metadata.of(this)));
  }

  public int health() {
    return health;
  }

  /**
   * Try to subtract the specified amount of damage from this structure's health.
   * If health drops at zero or below then the structure will shatter.
   * Note: Provide a non positive damage value to instantly destroy the structure.
   * @param damage the amount of damage to inflict
   * @return the remaining structure health
   */
  private int damageStructure(int damage) {
    if (damage > 0 && health > damage) {
      health -= damage;
      return health;
    }
    destroyStructure(this);
    return 0;
  }

  public static boolean tryDamageStructure(@NotNull Iterable<Block> blocks, int damage) {
    for (Block block : blocks) {
      if (block.hasMetadata(Metadata.DESTRUCTIBLE)) {
        FragileStructure structure = (FragileStructure) block.getMetadata(Metadata.DESTRUCTIBLE).get(0).value();
        if (structure != null) {
          structure.damageStructure(damage);
          return true;
        }
      }
    }
    return false;
  }

  public static void destroyStructure(@NotNull FragileStructure data) {
    for (Block block : data.fragileBlocks) {
      block.removeMetadata(Metadata.DESTRUCTIBLE, ProjectKorra.plugin);
      if (!data.predicate.test(block)) {
        continue;
      }
      BlockData blockData = block.getType().createBlockData();
      new TempBlock(block, Material.AIR.createBlockData());
      block.removeMetadata(Metadata.DESTRUCTIBLE, ProjectKorra.plugin);
      Location center = block.getLocation().add(0.5, 0.5, 0.5);
      ParticleEffect.BLOCK_CRACK.display(center, 2, 0.3, 0.3, 0.3, blockData);
      if (ThreadLocalRandom.current().nextInt(3) == 0) {
        block.getWorld().playSound(center, blockData.getSoundGroup().getBreakSound(), 2, 1);
      }
    }
  }

  public static @NotNull Builder builder() {
    return new Builder();
  }

  @Override
  public @NotNull Iterator<Block> iterator() {
    return fragileBlocks.iterator();
  }

  public static final class Builder {
    private Predicate<Block> predicate;
    private int health = 10;

    private Builder() {
    }

    public @NotNull Builder health(int health) {
      this.health = Math.max(1, health);
      return this;
    }

    public @NotNull Builder predicate(@NotNull Predicate<Block> predicate) {
      this.predicate = Objects.requireNonNull(predicate);
      return this;
    }

    public @Nullable FragileStructure build(@NotNull Collection<Block> blocks) {
      if (blocks.isEmpty()) {
        return null;
      }
      return new FragileStructure(Set.copyOf(blocks), predicate, health);
    }
  }
}

