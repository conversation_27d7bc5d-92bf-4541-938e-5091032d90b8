package com.projectkorra.projectkorra.util;

import com.projectkorra.projectkorra.ProjectKorra;
import org.bukkit.metadata.FixedMetadataValue;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public final class Metadata {
  public static final String DESTRUCTIBLE = "projectkorra-destructible";

  private Metadata() {
  }

  public static @NotNull FixedMetadataValue empty() {
    return of(null);
  }

  public static @NotNull FixedMetadataValue of(@Nullable Object obj) {
    return new FixedMetadataValue(ProjectKorra.plugin, obj);
  }
}
