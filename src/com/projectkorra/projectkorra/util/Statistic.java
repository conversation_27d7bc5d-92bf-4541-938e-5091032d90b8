package com.projectkorra.projectkorra.util;

import com.projectkorra.projectkorra.ability.CoreAbility;

public enum Statistic {

	TOTAL_LEVEL("TotalLevel", "total level"),
	ABILITY_LEVEL("AbilityLevel", "ability level"),
	ABILITY_USES("AbilityUses", "ability uses");

	private String name;
	private String displayName;

	private Statistic(final String name, final String displayName) {
		this.name = name;
		this.displayName = displayName;
	}

	public String getName() {
		return this.name;
	}

	public String getDisplayName() {
		return this.displayName;
	}

	public String getStatisticName(final CoreAbility ability) {
		return this.getName() + "_" + ability.getName();
	}


	public static Statistic getStatistic(final String name) {
		for (final Statistic statistic : Statistic.values()) {
			if (statistic.getName().equalsIgnoreCase(name)) {
				return statistic;
			}
		}
		return null;
	}

}
