package com.projectkorra.projectkorra.event;

import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

public class BendingRestrictEvent extends Event implements Cancellable {
	private static final HandlerList handlers = new HandlerList();

  private final Player source;
	private final Entity entity;

  private boolean cancelled = false;

  public BendingRestrictEvent(final Player source, final Entity entity) {
    this.source = source;
		this.entity = entity;
	}

	public Entity getEntity() {
		return this.entity;
	}

	public Player getSource() {
		return this.source;
	}

	@Override
	public HandlerList getHandlers() {
		return handlers;
	}

	public static HandlerList getHandlerList() {
		return handlers;
	}

	@Override
	public boolean isCancelled() {
		return this.cancelled;
	}

	@Override
	public void setCancelled(final boolean cancelled) {
		this.cancelled = cancelled;
	}
}
