package com.projectkorra.projectkorra.command;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.Manager;
import com.projectkorra.projectkorra.ability.Ability;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.util.Statistic;
import com.projectkorra.projectkorra.util.StatisticsManager;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Executor for /bending admin. Extends {@link PKCommand}.
 */
public class BendingAdmin extends PKCommand {
	public BendingAdmin() {
		super("admin", "/bending admin <Level>/<Uses> <Ability> <User> <Value>", ChatColor.YELLOW + "Set a player's level or uses statistics to a specific value.", new String[]{ "admin" });
	}

	@Override
	public void execute(final CommandSender sender, final List<String> args) {
		if (!hasPermission(sender) || !correctLength(sender, args.size(), 2, 4)) {
			return;
		}

		// Validate arguments
		String arg0 = args.get(0); // Confirm level or uses was entered
		boolean level;
		if (arg0.equalsIgnoreCase("uses")) {
			level = false;
		} else if (arg0.equalsIgnoreCase("level")) {
			level = true;
		} else {
			help(sender, false);
			return;
		}

		final CoreAbility ability = CoreAbility.getAbility(args.get(1));
		if (ability == null) { // Check to make sure the arg returns as a valid ability name
			help(sender, false);
			return;
		}
		String abilityName = ability.getName();

		Player target = Bukkit.getPlayer(args.get(2)); // Get target player
		if (target == null) {
			sender.sendMessage(ChatColor.RED + "Error: Cannot set stats for offline players.");
			return;
		}
		UUID targetUUID = target.getUniqueId();

		int value;
		try {
			value = Integer.parseInt(args.get(3)); // Get new value to set
			if (value < 0) { //Check the value is in range (positive integer)
				help(sender, false);
				return;
			}
		} catch (NumberFormatException e) {
			help(sender, false);
			return;
		}

		//check if the target level is above 10 (unsafe levels) and if it is, check if they have special permission to do this
		if (value > 10 && !sender.hasPermission("bending.command.admin.unsafe")) {
			sender.sendMessage(ChatColor.RED + "Error: You lack permission to set levels higher than 10.");
			return;
		}

		boolean toggledOn = false;
		final BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(target);

		if (!bPlayer.isToggled()) { // Turn their bending off to avoid stat write contention
			toggledOn = true;
			bPlayer.toggleBending();
		}

		StatisticsManager manager = Manager.getManager(StatisticsManager.class);
		if (level) {
			long abilityLevel = manager.getStatisticCurrent(targetUUID, StatisticsMethods.getId("AbilityLevel_" + abilityName));
			long currentTotalLevel = manager.getStatisticCurrent(targetUUID, StatisticsMethods.getId("TotalLevel"));
			long delta = value - abilityLevel;
			//add or remove levels,
			if (abilityLevel < value || abilityLevel > value && (currentTotalLevel + delta) > -1) {
				final int statId = StatisticsMethods.setStatisticTotalLevel(targetUUID, currentTotalLevel + delta);
				if (statId > 0) {
					manager.adminSave(targetUUID, statId, currentTotalLevel + delta, true);
					manager.clearStatDelta(targetUUID, statId);
				}
			}
		}
		final int statId = StatisticsMethods.setStatisticAbility(targetUUID, ability, level ? Statistic.ABILITY_LEVEL : Statistic.ABILITY_USES, value);
		if (statId > 0) {
			manager.adminSave(targetUUID, statId, value, true);
			manager.clearStatDelta(targetUUID, statId);
		}


		if (toggledOn) { // Turn their bending back on
			bPlayer.toggleBending();
		}

		String type = level ? "level" : "uses";
		GeneralMethods.sendBrandingMessage(sender,ChatColor.AQUA + "Successfully set " + target.getName() + "'s " + abilityName + " " + type + " to " + value + ".");
		if (!sender.getName().equals(target.getName())) {
			GeneralMethods.sendBrandingMessage(target, ChatColor.AQUA + "Your " + abilityName + " " + type + " has been set to " + value + ".");
		}
	}

	@Override
	protected List<String> getTabCompletion(final CommandSender sender, final List<String> args) {
		if (args.size() > 2 || !sender.hasPermission("bending.command.admin")) {
			return Collections.emptyList();
		}

		if (args.size() == 0) {
			return Arrays.asList("level", "uses");
		} else if (args.size() == 1) {
			return CoreAbility.getAbilities().stream()
				.filter(a -> !a.isHiddenAbility() && a.isEnabled())
				.map(Ability::getName).distinct().collect(Collectors.toList());
		} else if (args.size() == 2) {
			return Bukkit.getOnlinePlayers().stream().map(Player::getName).collect(Collectors.toList());
		}
		return Collections.emptyList();
	}
}
