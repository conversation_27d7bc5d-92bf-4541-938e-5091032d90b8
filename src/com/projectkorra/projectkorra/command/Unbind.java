package com.projectkorra.projectkorra.command;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

import org.bukkit.Bukkit;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.Element;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;

/**
 * Executor for /bending unbind. Extends {@link PKCommand}.
 */
public class Unbind extends PKCommand {

	
	String error = "§6Proper Usage: §3Use /bending unbind <ability> <user> to unbind a specific ability from a user's binds (regardless of which slot).";		
	private final String noBinds;

	public Unbind() {
		super("unbind", "/bending unbind <Ability> <user>", ConfigManager.languageConfig.get().getString("Commands.Display.Description"), new String[] { "unbind", "u", "un" });

		this.noBinds = ConfigManager.languageConfig.get().getString("Commands.Display.NoBinds");
	}

	@SuppressWarnings("deprecation")
	@Override
	public void execute(final CommandSender sender, final List<String> args) {
		//if (!this.hasPermission(sender) || !this.correctLength(sender, args.size(), 0, 1)) {
		//	return;
		//}

		// bending display [Element].
		if (args.size() == 0) {
			sender.sendMessage(error);
		}
		else if (args.size() == 1) {
			//Check to make sure the arg returns as a valid ability name
			final CoreAbility ability = CoreAbility.getAbility(args.get(0));
			if (ability == null) {
				sender.sendMessage(error);
			}
			else {
				//listBinds(sender, args.get(0));
				sender.sendMessage("Trying to unbind generic?");
			}
			
		}
		else if (args.size() == 2) {
			UUID user = Bukkit.getOfflinePlayer(args.get(1)).getUniqueId();
			
			//Check to make sure the arg returns as a valid ability name
			//also check the user returns valid
			final CoreAbility ability = CoreAbility.getAbility(args.get(0));
			if (ability == null || user == null) {
				sender.sendMessage(error);
			}
			else {
				removeBinds(sender, args.get(0), user, args.get(1));
			}
		}
		else {
			sender.sendMessage(error);
		}
	}


	
	private void removeBinds(final CommandSender sender, String abilityName, UUID user, String username) {
		BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(username.toLowerCase());
		if (bPlayer == null) {
			GeneralMethods.createBendingPlayer(user, username.toLowerCase());
			bPlayer = BendingPlayer.getBendingPlayer(username.toLowerCase());
		}
		
		Player player = Bukkit.getPlayerExact(username);
		if(player == null)
		{
			sender.sendMessage("§6Error: §3Cannot list bound ability levels for offline players.");
			return;
		}
		
		final HashMap<Integer, String> abilities = bPlayer.getAbilities();

		for (int i = 1; i <= 9; i++) {
			final String ability = abilities.get(i);
			
			if (ability != null) {
				if (ability.equalsIgnoreCase(abilityName)) {
					
					bPlayer.getAbilities().remove(i);
					GeneralMethods.saveAbility(bPlayer, i, null);
					if (sender.getName() == player.getName()) {
						sender.sendMessage("§6ProjectKorra §4" + ability+ " has been unbound.");
					}
					else {
						sender.sendMessage("§6ProjectKorra §4" + ability+ " has been unbound.");
						player.sendMessage("§6ProjectKorra §4" + ability+ " has been unbound automatically.");
					}
						
				}
			}
		}
	}
	
	@Override
	protected List<String> getTabCompletion(final CommandSender sender, final List<String> args) {
		if (args.size() >= 1 || !sender.hasPermission("bending.command.help")) {
			return new ArrayList<String>();
		}

		final List<String> list = new ArrayList<String>();
		for (final Element e : Element.getAllElements()) {
			list.add(e.getName());
		}

		final List<String> abils = new ArrayList<String>();
		for (final CoreAbility coreAbil : CoreAbility.getAbilities()) {
			if (!(sender instanceof Player) && (!coreAbil.isHiddenAbility()) && coreAbil.isEnabled() && !abils.contains(coreAbil.getName())) {
				abils.add(coreAbil.getName());
			} else if (sender instanceof Player) {
				if ((!coreAbil.isHiddenAbility()) && coreAbil.isEnabled() && !abils.contains(coreAbil.getName())) {
					abils.add(coreAbil.getName());
				}
			}
		}

		Collections.sort(abils);
		list.addAll(abils);
		return list;
	}
}
