package com.projectkorra.projectkorra.command;

import com.projectkorra.projectkorra.ProjectKorra;
import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;

import java.util.List;

/**
 * Executor for /bending reload. Extends {@link PKCommand}.
 */
public class ReloadLeaderboardCommand extends P<PERSON><PERSON>ommand {
	public ReloadLeaderboardCommand() {
		super("reloadleaderboard", "/bending reloadleaderboard", "Reload the leaderboard", new String[] { "reloaderleaderboard", "reloadleader", "rl" });
	}

	@Override
	public void execute(final CommandSender sender, final List<String> args) {
		if (!this.hasPermission(sender) || !this.correctLength(sender, args.size(), 0, 0)) {
			return;
		}
		ProjectKorra.plugin.leaderboard().reload();
		sender.sendMessage(ChatColor.AQUA + "Leaderboard has been reloaded.");
	}
}
