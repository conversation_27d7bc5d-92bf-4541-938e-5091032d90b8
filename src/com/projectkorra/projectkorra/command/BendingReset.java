package com.projectkorra.projectkorra.command;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.Manager;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.util.StatisticsManager;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.OfflinePlayer;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Executor for /bending reset. Extends {@link PKCommand}.
 */
public class BendingReset extends PKCommand {
	public BendingReset() {
		super("reset", "/bending reset <User>", "Reset a player's levels and uses to zero.", new String[]{ "reset" });
	}

	@Override
	public void execute(final CommandSender sender, final List<String> args) {
		if (!hasPermission(sender) || !correctLength(sender, args.size(), 1, 1)) {
			return;
		}

		String input = args.get(0);
		UUID uuid = null;
		String name = "";
		Player player = null;
		try {
			uuid = UUID.fromString(input);
			player = Bukkit.getPlayer(uuid); // Null if not online
		} catch (Exception ignore) {
		}
		if (uuid == null) { // Couldn't parse uuid, let's try player names
			// Check if online
			player = Bukkit.getPlayerExact(input);
			OfflinePlayer target = player;
			if (player == null) { // Let's try offline players
				target = Bukkit.getOfflinePlayerIfCached(input);
			}
			if (target == null) { // Couldn't find anybody
				sender.sendMessage(ChatColor.RED + "Error: Cannot find any player matching name or uuid for " + input);
				return;
			}
			name = target.getName();
			uuid = target.getUniqueId();
		}
		String nameWithUUID = name + " (" + uuid + ")";
		ProjectKorra.log.info("Resetting levels for " + nameWithUUID);
		Manager.getManager(StatisticsManager.class).resetAllStatistics(uuid);
		GeneralMethods.sendBrandingMessage(sender,ChatColor.AQUA + "Successfully reset levels and uses for" + nameWithUUID);
		if (player != null && !sender.getName().equalsIgnoreCase(name)) {
			GeneralMethods.sendBrandingMessage(player, ChatColor.AQUA + "Your levels and ability uses have been reset.");
		}
	}

	@Override
	protected List<String> getTabCompletion(final CommandSender sender, final List<String> args) {
		if (args.size() > 2 || !sender.hasPermission("bending.command.reset")) {
			return Collections.emptyList();
		}
		return Bukkit.getOnlinePlayers().stream().map(Player::getName).collect(Collectors.toList());
	}
}
