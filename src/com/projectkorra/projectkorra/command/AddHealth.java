package com.projectkorra.projectkorra.command;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import org.bukkit.Bukkit;
import org.bukkit.attribute.Attribute;
import org.bukkit.attribute.AttributeInstance;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import com.projectkorra.projectkorra.Element;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;

/**
 * Executor for /bending health. Extends {@link PKCommand}.
 */
public class AddHealth extends PKCommand {

	String error = "§6Proper Usage: §3/bending health <player> <add/remove> <amount>";
	private final String noBinds;
	UUID user;
	double value;
	
	public AddHealth() {
		super("health", "/bending health <player> <set/add/remove> <amount>", ConfigManager.languageConfig.get().getString("Commands.Display.Description"), new String[] { "health"});

		this.noBinds = ConfigManager.languageConfig.get().getString("Commands.Display.NoBinds");
	}

	@SuppressWarnings("deprecation")
	@Override
	public void execute(final CommandSender sender, final List<String> args) {
		if (!sender.hasPermission("bending.admin.health")) {
			sender.sendMessage("§4You do not have permission to do this!");
			return;
		}
		
		
		if (args.size() == 3) {
			
			user = Bukkit.getOfflinePlayer(args.get(0)).getUniqueId();
			
			
			if (user != null) {
				String type = args.get(1);
				
				if (type.equalsIgnoreCase("set") || type.equalsIgnoreCase("add") || type.equalsIgnoreCase("remove")) {
					
					try {
						value = Integer.parseInt(args.get(2));
				    }
				    catch (NumberFormatException e) {
				    	sender.sendMessage(error);
				    }
					
					if (value >= 0) {
							
							user = Bukkit.getOfflinePlayer(args.get(0)).getUniqueId();
						
							//pull current max health
							AttributeInstance temp = Bukkit.getPlayer(user).getAttribute(Attribute.MAX_HEALTH);
							
							double maxHealth = temp.getValue();
							
							
							if (type.equalsIgnoreCase("add")) {
								value = value + maxHealth;
							}
							if (type.equalsIgnoreCase("remove")) {
								
								//confirm health is valid. Can't bring max health below 0 and must be min 0.5
								if (maxHealth > value && maxHealth - value >= 0.5) {
									value = maxHealth - value;
								}
								else {
									sender.sendMessage("§6Project Korra §bPlayer health cannot be less than .5!");
									
								}
							}
							
							//actually set max health to value
							Bukkit.getPlayer(user).getAttribute(Attribute.MAX_HEALTH).setBaseValue(value);
							
							sender.sendMessage("§6Project Korra §bSuccessfully set " + args.get(0) + "'s health to " + value/2 + " hearts.");
						
					}
					else {
						sender.sendMessage(error);
					}
				}
				else {
					sender.sendMessage(error);
				}
			}
			else {
				sender.sendMessage(error);
			}
		}
		else {
			sender.sendMessage(error);
		}
	}
	
	@Override
	protected List<String> getTabCompletion(final CommandSender sender, final List<String> args) {
		if (args.size() >= 1 || !sender.hasPermission("bending.command.help")) {
			return new ArrayList<String>();
		}

		final List<String> list = new ArrayList<String>();
		for (final Element e : Element.getAllElements()) {
			list.add(e.getName());
		}

		final List<String> abils = new ArrayList<String>();
		for (final CoreAbility coreAbil : CoreAbility.getAbilities()) {
			if (!(sender instanceof Player) && (!coreAbil.isHiddenAbility()) && coreAbil.isEnabled() && !abils.contains(coreAbil.getName())) {
				abils.add(coreAbil.getName());
			} else if (sender instanceof Player) {
				if ((!coreAbil.isHiddenAbility()) && coreAbil.isEnabled() && !abils.contains(coreAbil.getName())) {
					abils.add(coreAbil.getName());
				}
			}
		}

		Collections.sort(abils);
		list.addAll(abils);
		return list;
	}
}
