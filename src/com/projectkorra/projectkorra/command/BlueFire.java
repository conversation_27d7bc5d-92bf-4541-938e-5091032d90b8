package com.projectkorra.projectkorra.command;

import java.util.List;

import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import com.projectkorra.projectkorra.Element;
import com.projectkorra.projectkorra.ability.FireAbility;

/**
 * Executor for /bending reload. Extends {@link PKCommand}.
 */
public class BlueFire extends PKCommand {
	public BlueFire() {
		super("bluefire", "/bending bluefire", Element.FIRE.getColor() + "Toggle the power of blue fire, if you have it!", new String[] { "bluefire", "bf" });
	}

	@Override
	public void execute(final CommandSender sender, final List<String> args) {
		if (!this.hasPermission(sender) || !this.correctLength(sender, args.size(), 0, 0)) {
			return;
		}
		if (sender instanceof Player player) {
			FireAbility.toggleBlueFire(player);
		}
	}
}
