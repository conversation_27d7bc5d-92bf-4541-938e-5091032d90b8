package com.projectkorra.projectkorra.command;

import java.util.List;

import com.projectkorra.projectkorra.Element;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.chiblocking.passive.ChiPassive;
import org.bukkit.Bukkit;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class BlockChi extends PKCommand {
	public BlockChi() {
		super("blockchi", "/bending blockchi <player> [duration] [is_silent]", Element.CHI.getColor() + "Simulate a bending chi block with an optional duration override in milliseconds", new String[] { "blockchi", "bc" });
	}

	@Override
	public void execute(final CommandSender sender, final List<String> args) {
		if (!this.hasPermission(sender) || !this.correctLength(sender, args.size(), 1, 3)) {
			return;
		}
		Player player = Bukkit.getPlayer(args.get(0));
		if (player == null) {
			return;
		}
		long duration = ChiPassive.getDuration();
		if (args.size() > 1) {
			String rawDuration = args.get(1);
			try {
				duration = Long.parseLong(rawDuration);
			} catch (NumberFormatException ignore) {
				duration = 0;
			}
			if (duration <= 0 || duration > 20_000) {
				ProjectKorra.log.warning("Invalid chi block duration provided: " + rawDuration);
				return;
			}
		}
		boolean isSilent = false;
		if (args.size() >= 3) {
			String rawIsSilent = args.get(2);
			if (rawIsSilent.contains("true")) {
				isSilent = true;
			}
		}
		ChiPassive.blockChi(sender instanceof Player cmdSource ? cmdSource : null, player, duration, isSilent);
	}
}
