package com.projectkorra.projectkorra.command;

import java.util.*;
import java.util.stream.Collectors;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.Element;
import com.projectkorra.projectkorra.Manager;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.Ability;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.ability.ComboAbility;
import com.projectkorra.projectkorra.ability.SubAbility;
import com.projectkorra.projectkorra.ability.util.ComboManager;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.Statistic;
import com.projectkorra.projectkorra.util.StatisticsManager;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import net.md_5.bungee.api.chat.HoverEvent;
import net.md_5.bungee.api.chat.TextComponent;
import net.md_5.bungee.api.chat.hover.content.Text;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

/**
 * Executor for /bending level. Extends {@link PKCommand}.
 */
public class CheckLevels extends PKCommand {
	private static final String error = ChatColor.GOLD + "Proper Usage: " + ChatColor.DARK_AQUA + "Use /bending level [Player] to view level information for all bound moves or /bending level <Ability> [Player] to list level information for a specific ability.";
	private static int totalLevelStatID = -1;

	List<Element> elementList = (Arrays.asList(Element.getElements()));

	public CheckLevels() {
		super("level", "/bending level [element] [-combos] [Ability] [Player]", "View level information for your bound moves or a specific ability. Use flag -c to also show combos. Use /b level [element] to see all levels for that element.", new String[]{ "level", "lvl", "l" });
	}

	@Override
	public void execute(final CommandSender sender, final List<String> args) {
		if (!isPlayer(sender) || !correctLength(sender, args.size(), 0, 3)) {
			return;
		}

		if (totalLevelStatID < 0) {
			totalLevelStatID = StatisticsMethods.getId(Statistic.TOTAL_LEVEL.getName());
		}
    List<String> filteredArgs = new ArrayList<>(args);
    boolean showCombos = filteredArgs.removeIf(this::isComboFlag);
		if (filteredArgs.size() == 0) {
			listBinds(sender, (Player) sender, showCombos, null);
		} else if (filteredArgs.size() == 1 && elementList.contains(Element.getElement(filteredArgs.get(0)))) {
			listBinds(sender, (Player) sender, false, Element.getElement(filteredArgs.get(0)));
		}else if (filteredArgs.size() == 1) {
			String arg0 = filteredArgs.get(0);
			Player targetPlayer = Bukkit.getPlayer(arg0);
			if (targetPlayer == null) {
				CoreAbility ability = CoreAbility.getAbility(arg0);
				if (ability != null && !ability.isHiddenAbility()) {
					listMove(sender, ability, (Player) sender);
					return;
				}
				sender.sendMessage(ChatColor.RED + "Could not find player " + arg0);
			} else {
				listBinds(sender, targetPlayer, showCombos, null);
			}
		} else {
			Player targetPlayer = Bukkit.getPlayer(filteredArgs.get(1));
			if (targetPlayer == null) {
				sender.sendMessage(ChatColor.RED + "Could not find player " + filteredArgs.get(1));
			} else {
				CoreAbility ability = CoreAbility.getAbility(filteredArgs.get(0));
				if (ability != null && !ability.isHiddenAbility()) {
					listMove(sender, ability, targetPlayer);
				} else {
					sender.sendMessage(error);
				}
			}
		}
	}

	private final String[] comboFlag = new String[] {"-c", "-combo", "-combos"};

	private boolean isComboFlag(String value) {
		return Arrays.stream(comboFlag).anyMatch(s -> s.equalsIgnoreCase(value));
	}

	/**
	 * Displays a Player's bound abilities.
	 * @param sender The CommandSender to output the bound abilities to
	 */
	private void listBinds(final CommandSender sender, Player target, boolean showCombos, Element element) {
		BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(target);
		if (bPlayer == null) {
			sender.sendMessage(ChatColor.GOLD + "Error: " + ChatColor.DARK_AQUA + "Could not find player " + target.getName());
			return;
		}
		long totalLevel = Manager.getManager(StatisticsManager.class).getStatisticCurrent(target.getUniqueId(), totalLevelStatID);
		final HashMap<Integer, String> abilities = bPlayer.getAbilities();

		sender.sendMessage(TLBMethods.getTLBHeader());
		sender.sendMessage(" ");

		sender.sendMessage(ChatColor.ITALIC + target.getName() + "'s Ability Levels - " + ChatColor.LIGHT_PURPLE + "" + ChatColor.ITALIC + "Bending Level " + totalLevel);

		if (TLBMethods.isHandicapped(target)) {
		  long level = TLBMethods.getHandicapLevel(target);
			if (target.getName().equalsIgnoreCase(sender.getName())) {
				sender.sendMessage(ChatColor.DARK_RED + "" + ChatColor.ITALIC + "You currently have a handicap, all your abilities are capped at level " + level +".");
			} else {
				sender.sendMessage(ChatColor.DARK_RED + "" + ChatColor.ITALIC + "They currently have a handicap, all their abilities are capped at level " + level +".");
			}
		}
		sender.sendMessage(" ");

		if (element == null) {
			for (int i = 1; i <= 9; i++) {
				final CoreAbility coreAbil = CoreAbility.getAbility(abilities.get(i));
				if (coreAbil == null) continue;
				sender.spigot().sendMessage(getAbilityInfo(bPlayer, coreAbil));
			}
		}
	

		if (showCombos) {
		Set<Element> elements = new HashSet<>(bPlayer.getElements());
		elements.addAll(bPlayer.getSubElements());
		for (var combo : ComboManager.getComboAbilities().keySet()) {
			CoreAbility coreAbil = CoreAbility.getAbility(combo);
			if (coreAbil == null || !elements.contains(coreAbil.getElement()) || coreAbil.isHiddenAbility()) {
			continue;
			}
			sender.spigot().sendMessage(getAbilityInfo(bPlayer, coreAbil));
		}
		}


		if (element != null) {
			final List<CoreAbility> elementAbilities = CoreAbility.getAbilitiesByElement(element);

			final HashSet<String> abilitiesSent = new HashSet<String>(); 
			for (final CoreAbility ability : elementAbilities) {
				if (ability.isHiddenAbility() || abilitiesSent.contains(ability.getName())) {
					continue;
				}
				sender.spigot().sendMessage(getAbilityInfo(bPlayer, ability));
				abilitiesSent.add(ability.getName());
			}
		}
 	

		sender.sendMessage(" ");
		sender.sendMessage(TLBMethods.getTLBFooter());
	}

	//check other player's move
	private void listMove(final CommandSender sender, CoreAbility ability, Player target) {
		BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(target);
		if (bPlayer == null) {
			sender.sendMessage(ChatColor.GOLD + "Error: " + ChatColor.DARK_AQUA + "Could not find player " + target.getName());
			return;
		}
		long totalLevel = Manager.getManager(StatisticsManager.class).getStatisticCurrent(bPlayer.getPlayer().getUniqueId(), totalLevelStatID);

		sender.sendMessage(TLBMethods.getTLBHeader());
		sender.sendMessage(" ");
		sender.sendMessage(ChatColor.ITALIC + target.getName() + "'s Ability Level - " + ChatColor.LIGHT_PURPLE + "" + ChatColor.ITALIC + "Bending Level " + totalLevel);
		sender.sendMessage(" ");
		sender.spigot().sendMessage(getAbilityInfo(bPlayer, ability));
		sender.sendMessage(" ");
		sender.sendMessage(TLBMethods.getTLBFooter());
	}

	private TextComponent getAbilityInfo(BendingPlayer bendingPlayer, CoreAbility ability) {
		//get the ability_use stat ID
		int statLevel = StatisticsMethods.getId("AbilityLevel_" + ability.getName());
		int statId = StatisticsMethods.getId("AbilityUses_" + ability.getName());

		if (statLevel == 0) {
			StatisticsMethods.addStatisticAbility(bendingPlayer.getPlayer().getUniqueId(), ability, Statistic.ABILITY_LEVEL, 0);
		}

		long currentLevel = Manager.getManager(StatisticsManager.class).getStatisticCurrent(bendingPlayer.getPlayer().getUniqueId(), statLevel);
		long currentUses = Manager.getManager(StatisticsManager.class).getStatisticCurrent(bendingPlayer.getPlayer().getUniqueId(), statId);
		int levelReq = ConfigManager.RPGConfig.get().getInt(ability.getName() + ".req." + (currentLevel + 1));

		TextComponent abilityInfo = new TextComponent(ability.getElement().getColor() + ability.getName()
		+ ChatColor.GRAY + " - Level " + ChatColor.AQUA + currentLevel
		+ ChatColor.GRAY + " with " + ChatColor.AQUA + currentUses
		+ ChatColor.GRAY + ((levelReq == 0) ? " uses. " + ChatColor.AQUA + "MASTERED!" : " / " + levelReq + " total uses."));

		abilityInfo.setHoverEvent( new HoverEvent( HoverEvent.Action.SHOW_TEXT, new Text( "Requires " + ConfigManager.RPGConfig.get().getInt(ability.getName() + ".req." + (10)) + " total uses to master this move!" ) ) );

		return abilityInfo;
	}

	@Override
	protected List<String> getTabCompletion(final CommandSender sender, final List<String> args) {
		if (!(sender instanceof Player) || args.size() > 1) {
			return Collections.emptyList();
		}
		List<String> completions = Bukkit.getOnlinePlayers().stream().map(Player::getName).collect(Collectors.toList());
		if (args.size() < 1) {
			CoreAbility.getAbilities().stream()
				.filter(a -> !a.isHiddenAbility() && a.isEnabled())
				.map(Ability::getName).distinct()
				.forEach(completions::add);
		}
		return completions;
	}
}
