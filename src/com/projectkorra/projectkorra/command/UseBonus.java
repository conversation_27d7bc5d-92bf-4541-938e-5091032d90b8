package com.projectkorra.projectkorra.command;

import java.util.List;

import com.projectkorra.projectkorra.TLBMethods;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;

import com.projectkorra.projectkorra.Element;

/**
 * Executor for /bending reload. Extends {@link PKCommand}.
 */
public class UseBonus extends PKCommand {

	public UseBonus() {
		super("bonus", "/bending bonus <enable/disable/reload> [source]", Element.AVATAR.getColor() + "Toggle bending xp bonus", new String[] { "bonus" });
	}

	@Override
	public void execute(final CommandSender sender, final List<String> args) {
		if (!sender.hasPermission("bending.admin")) {
			sender.sendMessage(noPermissionMessage);
			return;
		}

		if (!hasPermission(sender) || !correctLength(sender, args.size(), 1, 2)) {
			return;
		}

		String arg0 = args.get(0);
		if (arg0.equalsIgnoreCase("enable")) {
			long duration = 1200;
			TLBMethods.runBonus(1200);

			String message = ChatColor.AQUA + "" + ChatColor.BOLD + "BENDING XP BONUS"
				+ "\n" + ChatColor.GRAY + "Enjoy " + ChatColor.LIGHT_PURPLE + (duration / 60) + ChatColor.GRAY + " minutes of double bending XP (uses)"
				+ ((args.size() > 1) ? ChatColor.GRAY + " Courtesy of " + ChatColor.DARK_PURPLE + args.get(1) + ChatColor.GRAY + "!" : "");

			broadcastInfo(message);
		} else if (arg0.equalsIgnoreCase("disable")) {

			String message = ChatColor.AQUA + "" + ChatColor.BOLD + "BENDING XP BONUS FINISHED"
				+ "\n" + ChatColor.GRAY + "Bending XP has returned to normal.";

			broadcastInfo(message);
		} else if (arg0.equalsIgnoreCase("reload")) {
      TLBMethods.loadBonuses();
    } else {
			help(sender, false);
		}
	}

	private void broadcastInfo(String content) {
		Bukkit.broadcastMessage(TLBMethods.getTLBHeader());
		Bukkit.broadcastMessage("\n" + content + "\n");
		Bukkit.broadcastMessage(TLBMethods.getTLBFooter());
	}
}
