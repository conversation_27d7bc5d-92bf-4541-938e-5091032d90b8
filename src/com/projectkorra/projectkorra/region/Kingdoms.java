package com.projectkorra.projectkorra.region;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.kingdoms.constants.land.Land;
import org.kingdoms.constants.land.location.SimpleChunkLocation;
import org.kingdoms.constants.player.KingdomPlayer;

import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;

class Kingdoms extends RegionProtectionBase {

  protected Kingdoms() {
    super("Kingdoms", "RespectKingdoms");
  }

  @Override
  public boolean isRegionProtectedReal(Player player, Location location, CoreAbility ability, boolean igniteAbility, boolean explosiveAbility) {
    try {
      // Get the KingdomPlayer instance
      KingdomPlayer kingdomPlayer = KingdomPlayer.getKingdomPlayer(player);
      if (kingdomPlayer == null) {
        return false;
      }

      // Get the land at the location
      SimpleChunkLocation chunkLocation = new SimpleChunkLocation(
          location.getWorld().getName(),
          location.getChunk().getX(),
          location.getChunk().getZ()
      );
      Land land = Land.getLand(chunkLocation);

      // If no land is claimed at this location, allow bending
      if (land == null) {
        return false;
      }

      // Check if invasions should be protected during invasions
      boolean protectDuringInvasions = ConfigManager.defaultConfig.get().getBoolean("Properties.RegionProtection.Kingdoms.ProtectDuringInvasions", false);
      if (protectDuringInvasions && land.getKingdom() != null && land.getKingdom().isBeingInvaded()) {
        return true;
      }

      // Check if the player has bending permissions in this land
      // Priority order: Custom bending permission > Build permission > Kingdom membership
      try {
        if (land.getKingdom() != null) {
          // Method 1: Check for custom bending permission first
          if (hasCustomBendingPermission(kingdomPlayer, land)) {
            return false; // Allow bending if custom permission is granted
          }

          // Method 2: Check if player is kingdom member with build permissions
          if (land.getKingdom().isMember(kingdomPlayer)) {
            // For kingdom members, check if they have build permissions
            // This allows kingdoms to control bending through existing build permissions
            try {
              // Try to check build permissions - this may vary by Kingdoms version
              return !canPlayerBuildInLand(kingdomPlayer, land);
            } catch (Exception buildCheckException) {
              // If build check fails, default to allowing bending for members
              return false;
            }
          } else {
            // Player is not a member - check if outsiders are allowed to bend
            return !allowOutsiderBending(land);
          }
        }
        // If no kingdom owns the land but it's claimed, prevent bending by default
        return true;
      } catch (Exception permissionException) {
        // Fallback: if we can't determine permissions, prevent bending to be safe
        return true;
      }

    } catch (Exception e) {
      // If there's any error with the Kingdoms API, default to allowing bending
      // This prevents breaking bending if there are API changes
      return false;
    }
  }

  /**
   * Check if the player has a custom bending permission in the kingdom
   * This could be implemented as a custom permission node or through Kingdoms' permission system
   */
  private boolean hasCustomBendingPermission(KingdomPlayer kingdomPlayer, Land land) {
    try {
      // Method 1: Check if player has a custom "bending" permission node
      if (kingdomPlayer.getPlayer() != null && kingdomPlayer.getPlayer().hasPermission("kingdoms.bending.allow")) {
        return true;
      }

      // Method 2: Check if the kingdom has a custom bending permission set
      // This would require extending Kingdoms with custom permissions
      // For now, we'll use a configuration-based approach

      // Method 3: Check if player's role in the kingdom allows bending
      // This could be implemented by checking role names or custom role permissions
      if (land.getKingdom() != null && land.getKingdom().isMember(kingdomPlayer)) {
        // Check if the player's role allows bending (this is a placeholder for custom implementation)
        return checkRoleBendingPermission(kingdomPlayer, land);
      }

      return false;
    } catch (Exception e) {
      return false;
    }
  }

  /**
   * Check if the player can build in the land (build permission check)
   */
  private boolean canPlayerBuildInLand(KingdomPlayer kingdomPlayer, Land land) {
    try {
      // This is a simplified check - in a real implementation, you'd use the actual Kingdoms API
      // For now, we assume kingdom members can build unless specifically restricted
      return land.getKingdom() != null && land.getKingdom().isMember(kingdomPlayer);
    } catch (Exception e) {
      return false;
    }
  }

  /**
   * Check if outsiders (non-kingdom members) are allowed to bend in this land
   */
  private boolean allowOutsiderBending(Land land) {
    try {
      // Check configuration setting for outsider bending
      boolean allowOutsiders = ConfigManager.defaultConfig.get().getBoolean("Properties.RegionProtection.Kingdoms.AllowOutsiderBending", false);
      return allowOutsiders;
    } catch (Exception e) {
      return false; // Default to not allowing outsiders
    }
  }

  /**
   * Check if the player's role in the kingdom allows bending
   * This is where you could implement custom role-based bending permissions
   */
  private boolean checkRoleBendingPermission(KingdomPlayer kingdomPlayer, Land land) {
    try {
      // Method 1: Check by role name (example implementation)
      // You could configure which roles are allowed to bend

      // Method 2: Check by custom permission nodes
      if (kingdomPlayer.getPlayer() != null) {
        // Check for role-specific bending permissions
        if (kingdomPlayer.getPlayer().hasPermission("kingdoms.role.bending")) {
          return true;
        }

        // Check for specific role names that allow bending
        // This would require getting the player's role in the kingdom
        // For now, we'll use a simple approach
      }

      return false; // Default to not allowing unless explicitly permitted
    } catch (Exception e) {
      return false;
    }
  }
}
