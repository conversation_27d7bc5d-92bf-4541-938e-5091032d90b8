package com.projectkorra.projectkorra.region;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.kingdoms.constants.land.Land;
import org.kingdoms.constants.land.location.SimpleChunkLocation;
import org.kingdoms.constants.player.KingdomPlayer;

import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;

class Kingdoms extends RegionProtectionBase {

  protected Kingdoms() {
    super("Kingdoms", "RespectKingdoms");
  }

  @Override
  public boolean isRegionProtectedReal(Player player, Location location, CoreAbility ability, boolean igniteAbility, boolean explosiveAbility) {
    try {
      // Get the KingdomPlayer instance
      KingdomPlayer kingdomPlayer = KingdomPlayer.getKingdomPlayer(player);
      if (kingdomPlayer == null) {
        return false;
      }

      // Get the land at the location
      SimpleChunkLocation chunkLocation = new SimpleChunkLocation(
          location.getWorld().getName(),
          location.getChunk().getX(),
          location.getChunk().getZ()
      );
      Land land = Land.getLand(chunkLocation);

      // If no land is claimed at this location, allow bending
      if (land == null) {
        return false;
      }

      // Check if invasions should be protected during invasions
      boolean protectDuringInvasions = ConfigManager.defaultConfig.get().getBoolean("Properties.RegionProtection.Kingdoms.ProtectDuringInvasions", false);
      if (protectDuringInvasions && land.getKingdom() != null && land.getKingdom().isBeingInvaded()) {
        return true;
      }

      // Check if the player has build permissions in this land
      // If the player doesn't have build permissions, prevent bending
      // Try different API methods to check build permissions
      try {
        // Method 1: Check if player can build using the land's permission system
        if (land.getKingdom() != null) {
          // If the land belongs to a kingdom, check if player is a member with build permissions
          if (land.getKingdom().isMember(kingdomPlayer)) {
            // Player is a member, check their role permissions
            return false; // Allow bending for kingdom members
          } else {
            // Player is not a member, check if outsiders can build
            return true; // Prevent bending for non-members
          }
        }
        // If no kingdom owns the land but it's claimed, prevent bending
        return true;
      } catch (Exception permissionException) {
        // Fallback: if we can't determine permissions, prevent bending to be safe
        return true;
      }

    } catch (Exception e) {
      // If there's any error with the Kingdoms API, default to allowing bending
      // This prevents breaking bending if there are API changes
      return false;
    }
  }
}
