package com.projectkorra.projectkorra.region;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.kingdoms.constants.land.Land;
import org.kingdoms.constants.land.location.SimpleChunkLocation;
import org.kingdoms.constants.player.KingdomPlayer;

import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.hooks.KingdomsHook;

class Kingdoms extends RegionProtectionBase {

  // Custom bending permission is registered via KingdomsHook during plugin load

  protected Kingdoms() {
    super("Kingdoms", "RespectKingdoms");
  }

  @Override
  public boolean isRegionProtectedReal(Player player, Location location, CoreAbility ability, boolean igniteAbility, boolean explosiveAbility) {
    try {
      // Get the KingdomPlayer instance
      KingdomPlayer kingdomPlayer = KingdomPlayer.getKingdomPlayer(player);
      if (kingdomPlayer == null) {
        return false;
      }

      // Get the land at the location
      SimpleChunkLocation chunkLocation = new SimpleChunkLocation(
          location.getWorld().getName(),
          location.getChunk().getX(),
          location.getChunk().getZ()
      );
      Land land = Land.getLand(chunkLocation);

      // If no land is claimed at this location, allow bending
      if (land == null || !land.isClaimed()) {
        return false;
      }

      // Check if invasions should be protected during invasions
      boolean protectDuringInvasions = ConfigManager.defaultConfig.get().getBoolean("Properties.RegionProtection.Kingdoms.ProtectDuringInvasions", false);
      if (protectDuringInvasions && land.getKingdom() != null && land.getKingdom().isBeingInvaded()) {
        return true;
      }

      // Main logic: Check if player has permission to bend in this land
      if (land.getKingdom() != null) {
        // Check if player has the custom bending permission
        if (hasCustomBendingPermission(kingdomPlayer, land)) {
          return false; // Allow bending
        }

        // If player is a kingdom member, allow bending (they have access to the land)
        if (land.getKingdom().isMember(kingdomPlayer)) {
          return false; // Allow bending for kingdom members
        }

        // Player is not a member - check if outsiders can bend
        boolean allowOutsiders = ConfigManager.defaultConfig.get().getBoolean("Properties.RegionProtection.Kingdoms.AllowOutsiderBending", false);
        return !allowOutsiders; // Prevent bending if outsiders not allowed
      }

      // If we reach here, something went wrong - default to preventing bending
      return true;

    } catch (Exception e) {
      // If there's any error with the Kingdoms API, default to allowing bending
      // This prevents breaking bending if there are API changes
      return false;
    }
  }

  /**
   * Check if the player has a custom bending permission in the kingdom
   * This uses the KingdomsX permission registry system
   */
  private boolean hasCustomBendingPermission(KingdomPlayer kingdomPlayer, Land land) {
    try {
      // Use the KingdomsHook to check for custom bending permission
      if (KingdomsHook.hasCustomBendingPermission(kingdomPlayer)) {
        return true;
      }

      // Fallback: Check if player has a simple permission node
      if (kingdomPlayer.getPlayer() != null && kingdomPlayer.getPlayer().hasPermission("kingdoms.bending.allow")) {
        return true;
      }

      return false;
    } catch (Exception e) {
      return false;
    }
  }
}
