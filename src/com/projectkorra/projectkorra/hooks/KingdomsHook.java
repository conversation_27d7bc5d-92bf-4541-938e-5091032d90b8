package com.projectkorra.projectkorra.hooks;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;

/**
 * Hook for KingdomsX integration
 * Handles registration of custom permissions and other KingdomsX features
 * Uses reflection to work with different KingdomsX versions
 */
public class KingdomsHook {

    // Cached reflection objects
    private static Class<?> kingdomsClass;
    private static Class<?> namespaceClass;
    private static Class<?> kingdomPermissionClass;
    private static Object bendingPermission;
    private static Method getPermissionRegistryMethod;
    private static Method registerMethod;
    private static boolean reflectionInitialized = false;

    // Permission name for configuration purposes
    private static final String BENDING_PERMISSION_NAME = "ProjectKorra:bending";

    /**
     * Register custom KingdomsX permissions
     * This must be called during the plugin's onLoad() phase
     */
    public static void registerKingdomsPermissions() {
        try {
            if (isKingdomsAvailable() && initializeReflection()) {
                // Create the bending permission using reflection
                Object namespace = createNamespace("ProjectKorra", "BENDING");
                bendingPermission = createKingdomPermission(namespace);

                // Register the permission
                Object kingdoms = getKingdomsInstance();
                Object permissionRegistry = getPermissionRegistryMethod.invoke(kingdoms);
                registerMethod.invoke(permissionRegistry, bendingPermission);

                System.out.println("Successfully registered ProjectKorra bending permission with KingdomsX");
            } else {
                System.out.println("KingdomsX detected - bending protection enabled (without custom permissions)");
            }
        } catch (Exception e) {
            System.out.println("Failed to register KingdomsX permissions: " + e.getMessage());
            // This is not critical, the plugin can still function without custom permissions
        }
    }

    /**
     * Check if KingdomsX is available and loaded
     */
    public static boolean isKingdomsAvailable() {
        try {
            Class.forName("org.kingdoms.main.Kingdoms");
            return org.kingdoms.main.Kingdoms.get() != null;
        } catch (ClassNotFoundException | NoClassDefFoundError e) {
            return false;
        }
    }

    /**
     * Check if a player has the custom bending permission in their kingdom
     */
    public static boolean hasCustomBendingPermission(org.kingdoms.constants.player.KingdomPlayer kingdomPlayer) {
        try {
            if (isKingdomsAvailable() && kingdomPlayer != null && bendingPermission != null) {
                // Use reflection to call kingdomPlayer.hasPermission(bendingPermission)
                Method hasPermissionMethod = kingdomPlayer.getClass().getMethod("hasPermission", Object.class);
                return (Boolean) hasPermissionMethod.invoke(kingdomPlayer, bendingPermission);
            }
        } catch (Exception e) {
            // If there's an error, fall back to default behavior
        }
        return false;
    }

    /**
     * Get the permission name for configuration purposes
     */
    public static String getBendingPermissionName() {
        return BENDING_PERMISSION_NAME;
    }

    /**
     * Initialize reflection objects for KingdomsX API
     */
    private static boolean initializeReflection() {
        if (reflectionInitialized) {
            return true;
        }

        try {
            // Load required classes
            kingdomsClass = Class.forName("org.kingdoms.main.Kingdoms");
            namespaceClass = Class.forName("org.kingdoms.constants.namespace.Namespace");
            kingdomPermissionClass = Class.forName("org.kingdoms.constants.kingdom.model.KingdomPermission");

            // Get methods
            getPermissionRegistryMethod = kingdomsClass.getMethod("getPermissionRegistry");

            // Get registry class and register method
            Class<?> registryClass = Class.forName("org.kingdoms.constants.kingdom.model.KingdomPermissionRegistry");
            registerMethod = registryClass.getMethod("register", kingdomPermissionClass);

            reflectionInitialized = true;
            return true;
        } catch (Exception e) {
            System.out.println("Failed to initialize KingdomsX reflection: " + e.getMessage());
            return false;
        }
    }

    /**
     * Create a Namespace object using reflection
     */
    private static Object createNamespace(String plugin, String name) throws Exception {
        Constructor<?> constructor = namespaceClass.getConstructor(String.class, String.class);
        return constructor.newInstance(plugin, name);
    }

    /**
     * Create a KingdomPermission object using reflection
     */
    private static Object createKingdomPermission(Object namespace) throws Exception {
        Constructor<?> constructor = kingdomPermissionClass.getConstructor(namespaceClass);
        return constructor.newInstance(namespace);
    }

    /**
     * Get the Kingdoms instance using reflection
     */
    private static Object getKingdomsInstance() throws Exception {
        Method getMethod = kingdomsClass.getMethod("get");
        return getMethod.invoke(null);
    }
}
