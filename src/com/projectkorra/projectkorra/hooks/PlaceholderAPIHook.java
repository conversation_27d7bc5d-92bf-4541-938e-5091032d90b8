package com.projectkorra.projectkorra.hooks;

import static java.util.stream.Collectors.joining;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.projectkorra.projectkorra.*;
import com.projectkorra.projectkorra.util.Statistic;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;

import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.object.Preset;
import com.projectkorra.projectkorra.util.StatisticsManager;

import me.clip.placeholderapi.expansion.PlaceholderExpansion;

public class PlaceholderAPIHook extends PlaceholderExpansion {

	private final ProjectKorra plugin;
	private final int totalLevelStatId;

	public PlaceholderAPIHook(final ProjectKorra plugin) {
		this.plugin = plugin;
		totalLevelStatId = StatisticsMethods.getId(Statistic.TOTAL_LEVEL.getName());
	}

	private int parseInt(String value) {
		if (value != null) {
			try {
				return Integer.parseInt(value);
			} catch (NumberFormatException ignore) {
			}
		}
		return 0;
	}

	@Override
	public String onPlaceholderRequest(final Player player, final String params) {
		if (params.startsWith("toplevels_")) {
			int number = parseInt(params.substring("toplevels_".length()));
			LevelLeaderboard.LeaderboardEntry entry = plugin.leaderboard().get(number);
			return entry == null ? "" : entry.format().legacy();
		}

		final BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
		if (bPlayer == null) {
			return "";
		}

		if (params.equals("totallevel")) {
			long totalLevel = Manager.getManager(StatisticsManager.class).getStatisticCurrent(bPlayer.getPlayer().getUniqueId(), totalLevelStatId);
			return ""+totalLevel;
		} else if (params.startsWith("slot")) {
			final String ability = bPlayer.getAbilities().get(Integer.parseInt(params.substring(params.length() - 1)));
			final CoreAbility coreAbil = CoreAbility.getAbility(ability);
			if (coreAbil == null) {
				return "";
			}
			return coreAbil.getElement().getColor() + coreAbil.getName();
		} else if (params.equals("element") || params.equals("elementcolor")) {
			String e = "Nonbender";
			ChatColor c = ChatColor.WHITE;
			if (player.hasPermission("bending.avatar") || (bPlayer.hasElement(Element.AIR) && bPlayer.hasElement(Element.EARTH) && bPlayer.hasElement(Element.FIRE) && bPlayer.hasElement(Element.WATER))) {
				c = Element.AVATAR.getColor();
				e = Element.AVATAR.getName();
			} else if (bPlayer.getElements().size() > 0) {
				c = bPlayer.getElements().get(0).getColor();
				e = bPlayer.getElements().get(0).getName();
			}
			if (params.equals("element")) {
				return e;
			} else if (params.equals("elementcolor")) {
				return c.toString();
			}
		} else if (params.equals("elements")) {
			return bPlayer.getElements().stream().map(item -> item.getColor() + item.getName()).collect(joining(" "));
		} 
		else if (params.equals("subelements")) {
			return bPlayer.getSubElements().stream().map(item -> item.getColor() + item.getName()).collect(joining(" "));
		}
		else if (params.equals("presets")) {

			final List<Preset> presets = Preset.presets.get(player.getUniqueId());
			final List<String> presetNames = new ArrayList<String>();

			if (presets == null || presets.isEmpty()) {
				return null;
			}
			
			for (final Preset preset : presets) {
				presetNames.add(preset.getName());
			}
			return presetNames.stream().collect(Collectors.joining(", "));
		}

		return null;
	}

	@Override
	public boolean persist() {
		return true;
	}

	@Override
	public boolean canRegister() {
		return true;
	}

	@Override
	public String getAuthor() {
		return this.plugin.getDescription().getAuthors().toString();
	}

	@Override
	public String getIdentifier() {
		return "ProjectKorra";
	}

	@Override
	public String getVersion() {
		return this.plugin.getDescription().getVersion();
	}
}
