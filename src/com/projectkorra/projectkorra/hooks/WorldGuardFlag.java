package com.projectkorra.projectkorra.hooks;

import com.sk89q.worldguard.WorldGuard;
import com.sk89q.worldguard.protection.flags.DoubleFlag;
import com.sk89q.worldguard.protection.flags.StateFlag;
import com.sk89q.worldguard.protection.flags.StringFlag;
import com.sk89q.worldguard.protection.flags.registry.FlagRegistry;

import com.projectkorra.projectkorra.ProjectKorra;

public class WorldGuardFlag {
	public static void registerBendingWorldGuardFlag() {
		final FlagRegistry registry = WorldGuard.getInstance().getFlagRegistry();
		try {
			registry.register(new StateFlag("bending", false));
		} catch (final Exception e) {
			ProjectKorra.log.severe("Unable to register bending WorldGuard flag: " + e);
		}
		try {
			registry.register(new StateFlag("unsafe-bending", true));
		} catch (final Exception e) {
			ProjectKorra.log.severe("Unable to register bending WorldGuard flag: " + e);
		}
		try {
			registry.register(new DoubleFlag("bending-level"));
		} catch (final Exception e) {
			ProjectKorra.log.severe("Unable to register bending WorldGuard flag: " + e);
		}
		try {
			registry.register(new StringFlag("bending-allow-move"));
		} catch (final Exception e) {
			ProjectKorra.log.severe("Unable to register bending WorldGuard flag: " + e);
		}
		
	}
}
