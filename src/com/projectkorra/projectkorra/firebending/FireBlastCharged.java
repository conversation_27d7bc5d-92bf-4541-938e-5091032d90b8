package com.projectkorra.projectkorra.firebending;

import java.util.Random;

import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.ability.FireAbility;
import com.projectkorra.projectkorra.attribute.Attribute;
import com.projectkorra.projectkorra.command.Commands;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;

public class FireBlastCharged extends FireAbility {
	private boolean charged;
	private boolean launched;
	private boolean dissipate;
	private long time;
	@Attribute(Attribute.CHARGE_DURATION)
	private long chargeTime;
	@Attribute(Attribute.COOLDOWN)
	private long cooldown;
	private long interval;
	@Attribute(Attribute.DAMAGE)
	private double maxDamage;
	@Attribute(Attribute.RANGE)
	private double range;
	@Attribute(Attribute.SPEED)
	private double speed;
	private double collisionRadius;
	@Attribute("Explosion" + Attribute.RANGE)
	private double explosionRadius;
	@Attribute(Attribute.FIRE_TICK)
	private double fireTicks;
	private Location origin;
	private Location location;
	private Vector direction;

	public FireBlastCharged(final Player player) {
		super(player);

		if (!this.bPlayer.canBend(this) || hasAbility(player, FireBlastCharged.class)) {
			return;
		}

		this.charged = false;
		this.launched = false;
		this.dissipate = getConfig().getBoolean("Abilities.Fire.FireBlast.Dissipate");
		this.time = System.currentTimeMillis();
		this.interval = 25;

		int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
		long currentLevel = TLBMethods.limitLevels(player, statLevel);

		this.chargeTime = TLBMethods.getLong("Abilities.Fire.FireBlast.Charged.ChargeTime", currentLevel);
		this.cooldown = TLBMethods.getLong("Abilities.Fire.FireBlast.Charged.Cooldown", currentLevel);
		this.collisionRadius = TLBMethods.getDouble("Abilities.Fire.FireBlast.Charged.CollisionRadius", currentLevel);
		this.maxDamage = TLBMethods.getDouble("Abilities.Fire.FireBlast.Charged.Damage", currentLevel);
		this.range = TLBMethods.getDouble("Abilities.Fire.FireBlast.Charged.Range", currentLevel);
		this.speed = TLBMethods.getDouble("Abilities.Fire.FireBlast.Charged.Speed", currentLevel);
		this.explosionRadius = TLBMethods.getDouble("Abilities.Fire.FireBlast.Charged.ExplosionRadius", currentLevel);
		this.fireTicks = TLBMethods.getDouble("Abilities.Fire.FireBlast.Charged.FireTicks", currentLevel);

		if (isDay(player.getWorld())) {
			this.chargeTime = (long) (this.chargeTime / getDayFactor());
			this.maxDamage = this.getDayFactor(this.maxDamage);
			this.range = this.getDayFactor(this.range);
		}
		if (this.bPlayer.isAvatarState()) {
			this.chargeTime = getConfig().getLong("Abilities.Avatar.AvatarState.Fire.FireBlast.Charged.ChargeTime");
			this.maxDamage = getConfig().getDouble("Abilities.Avatar.AvatarState.Fire.FireBlast.Charged.Damage");
		}

		if (!player.getEyeLocation().getBlock().isLiquid()) {
			this.start();
		}
	}

	public static boolean annihilateBlasts(final Location location, final double radius, final Player source) {
		boolean broke = false;
		for (final FireBlastCharged chargedBlast : getAbilities(FireBlastCharged.class)) {
			if (!chargedBlast.launched) {
				continue;
			}

			final Location fireBlastLocation = chargedBlast.location;
			if (location.getWorld().equals(fireBlastLocation.getWorld()) && !source.equals(chargedBlast.player)) {
				if (location.distanceSquared(fireBlastLocation) <= radius * radius) {
					chargedBlast.explode();
					broke = true;
				}
			}
		}
		return broke;
	}

	public static boolean isCharging(final Player player) {
		for (final FireBlastCharged chargedBlast : getAbilities(player, FireBlastCharged.class)) {
			if (!chargedBlast.launched) {
				return true;
			}
		}
		return false;
	}

	public static void removeFireballsAroundPoint(final Location location, final double radius) {
		for (final FireBlastCharged fireball : getAbilities(FireBlastCharged.class)) {
			if (!fireball.launched) {
				continue;
			}
			final Location fireblastlocation = fireball.location;
			if (location.getWorld().equals(fireblastlocation.getWorld())) {
				if (location.distanceSquared(fireblastlocation) <= radius * radius) {
					fireball.remove();
				}
			}
		}
	}

	public void explode() {
		double halfRadius = explosionRadius / 2;
		for (final Entity entity : GeneralMethods.getEntitiesAroundPoint(this.location, this.explosionRadius)) {
			if (entity instanceof Player && Commands.invincible.contains(entity.getName())) {
				continue;
			}
			if (entity instanceof LivingEntity) {
				Location entityCenter = entity.getLocation().add(0, entity.getHeight() / 2, 0);
				double distance = entityCenter.distance(this.location);
				double damage = maxDamage;
				if (distance > halfRadius) {
					damage = maxDamage * (1 - ((distance - halfRadius) / explosionRadius));
				}
				DamageHandler.damageEntity(entity, player, damage, this);
			}
		}
		this.location.getWorld().playSound(this.location, Sound.ENTITY_GENERIC_EXPLODE, 5, 1);
		ParticleEffect.EXPLOSION_HUGE.display(this.location, 1, 0, 0, 0);
		this.ignite(this.location);
		this.remove();
	}

	private void executeFireball() {
		for (final Block block : GeneralMethods.getBlocksAroundPoint(this.location, this.collisionRadius)) {
			playFirebendingParticles(block.getLocation(), 5, 0.5, 0.5, 0.5);
			if ((new Random()).nextInt(4) == 0) {
				playFirebendingSound(this.location);
				dryWetBlocks(block, this, true);
			} else {
				dryWetBlocks(block, this);
			}
		}

		boolean exploded = false;
		for (final Entity entity : GeneralMethods.getEntitiesAroundPoint(this.location, this.collisionRadius)) {
			if (GeneralMethods.isRegionProtectedFromBuild(this, entity.getLocation())) {
				remove();
				return;
			}
			if (entity instanceof LivingEntity && !entity.getUniqueId().equals(this.player.getUniqueId())) {
				exploded = true;
				break;
			}
		}
		if (exploded) {
			explode();
		}
	}

	private void ignite(final Location location) {
		for (final Block block : GeneralMethods.getBlocksAroundPoint(location, this.collisionRadius)) {
			if (BlazeArc.isIgnitable(block)) {
				if (block.getType() != getFireType()) {
					BlazeArc.getReplacedBlocks().put(block.getLocation(), block.getState());
				}
				block.setType(getFireType());
				if (this.dissipate) {
					BlazeArc.getIgnitedBlocks().put(block, this.player);
					BlazeArc.getIgnitedTimes().put(block, System.currentTimeMillis());
				}
			}
		}
	}

	@Override
	public void progress() {
		if (!this.bPlayer.canBendIgnoreBinds(this) && !this.launched) {
			this.remove();
			return;
		} else if (!this.bPlayer.canBendIgnoreCooldowns(CoreAbility.getAbility("FireBlast")) && !this.launched) {
			this.remove();
			return;
		} else if (!this.player.isSneaking() && !this.charged) {
			this.remove();
			return;
		}

		if (System.currentTimeMillis() > this.getStartTime() + this.chargeTime) {
			this.charged = true;
		}
		if (!this.player.isSneaking() && !this.launched) {
			this.launched = true;
			this.location = this.player.getEyeLocation();
			this.origin = this.location.clone();
			this.direction = this.location.getDirection().normalize().multiply(this.speed);
			this.bPlayer.addCooldown(this);
		}

		if (System.currentTimeMillis() > this.time + this.interval) {
			if (this.launched) {
				if (GeneralMethods.isRegionProtectedFromBuild(this, this.location)) {
					this.remove();
					return;
				}
			}

			this.time = System.currentTimeMillis();

			if (!this.launched && !this.charged) {
				return;
			} else if (!this.launched) {
				playFirebendingParticles(this.player.getEyeLocation().clone().add(this.player.getEyeLocation().getDirection().clone()), 1, 0.25, 0.25, 0.25);
				return;
			}

			if (GeneralMethods.checkDiagonalWall(this.location, this.direction)) {
				this.explode();
				return;
			}
			this.location = this.location.clone().add(this.direction);
			if (this.location.distanceSquared(this.origin) > this.range * this.range) {
				this.remove();
				return;
			}

			if (GeneralMethods.isSolid(this.location.getBlock())) {
				this.explode();
				return;
			} else if (this.location.getBlock().isLiquid()) {
				this.remove();
				return;
			}
			this.executeFireball();
		}
	}

	@Override
	public String getName() {
		return "FireBlast";
	}

	@Override
	public Location getLocation() {
		return this.location != null ? this.location : this.origin;
	}

	@Override
	public long getCooldown() {
		return this.cooldown;
	}

	@Override
	public boolean isSneakAbility() {
		return true;
	}

	@Override
	public boolean isHarmlessAbility() {
		return false;
	}

	@Override
	public boolean isCollidable() {
		return this.launched;
	}

	@Override
	public double getCollisionRadius() {
		return this.collisionRadius;
	}

	public boolean isCharged() {
		return this.charged;
	}

	public void setCharged(final boolean charged) {
		this.charged = charged;
	}

	public boolean isLaunched() {
		return this.launched;
	}

	public void setLaunched(final boolean launched) {
		this.launched = launched;
	}

	public boolean isDissipate() {
		return this.dissipate;
	}

	public void setDissipate(final boolean dissipate) {
		this.dissipate = dissipate;
	}

	public long getTime() {
		return this.time;
	}

	public void setTime(final long time) {
		this.time = time;
	}

	public long getChargeTime() {
		return this.chargeTime;
	}

	public void setChargeTime(final long chargeTime) {
		this.chargeTime = chargeTime;
	}

	public long getInterval() {
		return this.interval;
	}

	public void setInterval(final long interval) {
		this.interval = interval;
	}

	public double getMaxDamage() {
		return this.maxDamage;
	}

	public void setMaxDamage(final double maxDamage) {
		this.maxDamage = maxDamage;
	}

	public double getRange() {
		return this.range;
	}

	public void setRange(final double range) {
		this.range = range;
	}

	public void setCollisionRadius(final double collisionRadius) {
		this.collisionRadius = collisionRadius;
	}

	public double getExplosionRadius() {
		return this.explosionRadius;
	}

	public void setExplosionRadius(final double explosionRadius) {
		this.explosionRadius = explosionRadius;
	}

	public double getFireTicks() {
		return this.fireTicks;
	}

	public void setFireTicks(final double fireTicks) {
		this.fireTicks = fireTicks;
	}

	public Location getOrigin() {
		return this.origin;
	}

	public void setOrigin(final Location origin) {
		this.origin = origin;
	}

	public Vector getDirection() {
		return this.direction;
	}

	public void setDirection(final Vector direction) {
		this.direction = direction;
	}

	public void setLocation(final Location location) {
		this.location = location;
	}
}
