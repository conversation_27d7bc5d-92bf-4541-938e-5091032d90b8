package com.projectkorra.projectkorra.firebending.combo;

import java.util.ArrayList;
import java.util.List;

import com.projectkorra.projectkorra.util.ParticleEffect;
import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.ability.ComboAbility;
import com.projectkorra.projectkorra.ability.FireAbility;
import com.projectkorra.projectkorra.ability.util.Collision;
import com.projectkorra.projectkorra.ability.util.ComboManager.AbilityInformation;
import com.projectkorra.projectkorra.attribute.Attribute;
import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.util.StatisticsMethods;

public class FireKick extends FireAbility implements ComboAbility {

	@Attribute(Attribute.COOLDOWN)
	private long cooldown;
	@Attribute(Attribute.DAMAGE)
	private double damage;
	@Attribute(Attribute.SPEED)
	private double speed;
	@Attribute(Attribute.RANGE)
	private double range;
	private double collisionRadius;
	private int shatterRadius;
	private Location location;
	private Location destination;
	private ArrayList<LivingEntity> affectedEntities;
	private ArrayList<BukkitRunnable> tasks;

	private boolean flameParticles = true;

	public FireKick(final Player player) {
		super(player);

		if (!this.bPlayer.canBendIgnoreBindsCooldowns(this)) {
			return;
		}

		this.affectedEntities = new ArrayList<>();
		this.tasks = new ArrayList<>();

		int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
		long currentLevel = TLBMethods.limitLevels(player, statLevel);
		flameParticles = currentLevel < 10 || !hasBlueFire();

		this.damage = TLBMethods.getDouble("Abilities.Fire.FireKick.Damage", currentLevel);
		this.range = TLBMethods.getDouble("Abilities.Fire.FireKick.Range", currentLevel);
		this.cooldown = TLBMethods.getLong("Abilities.Fire.FireKick.Cooldown", currentLevel);
		this.speed = TLBMethods.getLong("Abilities.Fire.FireKick.Speed", currentLevel);
		this.collisionRadius = TLBMethods.getDouble("Abilities.Fire.FireKick.CollisionRadius", currentLevel);
		this.shatterRadius = TLBMethods.getInt("Abilities.Fire.FireKick.ShatterRadius", currentLevel);

		if (this.bPlayer.isAvatarState()) {
			this.cooldown = 0;
			this.damage = getConfig().getDouble("Abilities.Avatar.AvatarState.Fire.FireKick.Damage");
			this.range = getConfig().getDouble("Abilities.Avatar.AvatarState.Fire.FireKick.Range");
		}

		this.start();
	}

	@Override
	public String getName() {
		return "FireKick";
	}

	@Override
	public boolean isCollidable() {
		return true;
	}

	@Override
	public void progress() {
		if (!this.bPlayer.canBendIgnoreBindsCooldowns(this)) {
			this.remove();
			return;
		}

		for (int i = 0; i < this.tasks.size(); i++) {
			final BukkitRunnable br = this.tasks.get(i);
			if (br instanceof final FireComboStream fs) {
				if (fs.isCancelled()) {
					this.tasks.remove(fs);
				}
			}
		}

		if (this.destination == null) {
			if (this.bPlayer.isOnCooldown("FireKick") && !this.bPlayer.isAvatarState()) {
				this.remove();
				return;
			}

			this.bPlayer.addCooldown("FireKick", this.cooldown);
			GeneralMethods.shatterIce(this.player.getLocation(), this.shatterRadius);
			final Vector eyeDir = this.player.getEyeLocation().getDirection().normalize().multiply(this.range);
			this.destination = this.player.getEyeLocation().add(eyeDir);

			this.player.getWorld().playSound(this.player.getLocation(), Sound.ENTITY_HORSE_JUMP, 0.5f, 0f);
			this.player.getWorld().playSound(this.player.getLocation(), Sound.ENTITY_CREEPER_PRIMED, 0.5f, 1f);
			for (int i = -30; i <= 30; i += 5) {
				Vector vec = GeneralMethods.getDirection(this.player.getLocation(), this.destination.clone());
				vec = GeneralMethods.rotateXZ(vec, i);

				final FireComboStream fs = new FireComboStream(this.player, this, vec, this.player.getLocation(), this.range, this.speed);
				fs.setCollisionRadius(collisionRadius);
				fs.setSpread(0.2F);
				fs.setDensity(5);
				fs.setUseNewParticles(true);
				if (flameParticles) {
					fs.setParticleEffect(ParticleEffect.FLAME);
				}
				fs.setDamage(this.damage);
				if (this.tasks.size() % 3 != 0) {
					fs.setCollides(false);
				}
				fs.runTaskTimer(ProjectKorra.plugin, 0, 1L);
				this.tasks.add(fs);
				this.player.getWorld().playSound(this.player.getLocation(), Sound.ITEM_FLINTANDSTEEL_USE, 0.5f, 1f);
			}
		} else if (this.tasks.size() == 0) {
			this.remove();
		}

	}

	@Override
	public void remove() {
		super.remove();
		for (final BukkitRunnable task : this.tasks) {
			task.cancel();
		}
	}

	@Override
	public void handleCollision(final Collision collision) {
		if (collision.isRemovingFirst()) {
			final ArrayList<BukkitRunnable> newTasks = new ArrayList<>();
			final double collisionDistanceSquared = Math.pow(this.getCollisionRadius() + collision.getAbilitySecond().getCollisionRadius(), 2);
			// Remove all of the streams that are by this specific ourLocation.
			// Don't just do a single stream at a time or this algorithm becomes O(n^2) with Collision's detection algorithm.
			for (final BukkitRunnable task : this.getTasks()) {
				if (task instanceof final FireComboStream stream) {
					if (stream.getLocation().distanceSquared(collision.getLocationSecond()) > collisionDistanceSquared) {
						newTasks.add(stream);
					} else {
						stream.cancel();
					}
				} else {
					newTasks.add(task);
				}
			}
			this.setTasks(newTasks);
		}
	}

	@Override
	public List<Location> getLocations() {
		final ArrayList<Location> locations = new ArrayList<>();
		for (final BukkitRunnable task : this.getTasks()) {
			if (task instanceof final FireComboStream stream) {
				locations.add(stream.getLocation());
			}
		}
		return locations;
	}

	@Override
	public boolean isSneakAbility() {
		return true;
	}

	@Override
	public boolean isHarmlessAbility() {
		return false;
	}

	@Override
	public long getCooldown() {
		return this.cooldown;
	}

	@Override
	public Location getLocation() {
		return this.location;
	}

	@Override
	public Object createNewComboInstance(final Player player) {
		return new FireKick(player);
	}

	@Override
	public ArrayList<AbilityInformation> getCombination() {
		final ArrayList<AbilityInformation> fireKick = new ArrayList<>();
		fireKick.add(new AbilityInformation("FireBlast", ClickType.LEFT_CLICK));
		fireKick.add(new AbilityInformation("FireBlast", ClickType.LEFT_CLICK));
		fireKick.add(new AbilityInformation("FireBlast", ClickType.SHIFT_DOWN));
		fireKick.add(new AbilityInformation("FireBlast", ClickType.LEFT_CLICK));
		return fireKick;
	}

	public ArrayList<LivingEntity> getAffectedEntities() {
		return this.affectedEntities;
	}

	public ArrayList<BukkitRunnable> getTasks() {
		return this.tasks;
	}

	public void setTasks(final ArrayList<BukkitRunnable> tasks) {
		this.tasks = tasks;
	}
}
