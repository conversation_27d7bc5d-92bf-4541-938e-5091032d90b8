# ProjectKorra [![travis](https://travis-ci.org/ProjectKorra/ProjectKorra.svg?branch=master)](https://travis-ci.org/ProjectKorra/ProjectKorra)

![Core Icon](http://i.imgur.com/8XB8XHF.png)



## About ProjectKorra

ProjectKorra is a place for members across the Minecraft and Bending Communities to come together and interact. ProjectKorra is one of the biggest Minecraft and Avatar/Korra related communities around and is still growing rapidly. Our goal is to bridge the two communities, providing users with a way to implement aspects of the hit television shows "Avatar: The Last Airbender" and "The Legend of Korra" into Minecraft.

## Contributing

We are always eager to see active developers in the community making pull requests to ProjectKorra and invite you to do so. We just ask that all incoming pull requests be made to the `wip` branch and that you try your best to adhere to our pull request template. If you find yourself contributing a lot and want to get more involved with the plugins community you may want to consider joining our development team [here](http://projectkorra.com/join-the-team/). 

## Downloads

You can find the latest recommended build on the official [Projectkorra Downloads Page](http://projectkorra.com/downloads/).

Additionally, you can find the latest BETA builds in the resources section of the ProjectKorra forum, or you can use the Downloads page to download the latest. Stability of BETA builds are never promised, but usually anything pushed to the BETA Build section of the forum is at least runnable.

You can view the changelogs for the plugin you're running in the thread created for the build, or by viewing the master changelog for the version on the wiki. The changelog for that version will be up to date for all features up to the latest dev build on the forum. [Changelogs](http://projectkorra.com/wiki/index.php?title=Changelogs)

## Key Features

- Pick an element to play as, choose how you want to play.
  - Subelements are also made available. For example: Bloodbending and Icebending for Waterbenders, Metal and Lavabending for Earth, and so on.
- Battle others with your Bending
  - Chain abilities together to perform unique Combo abilities.
- Extensive permissions support. Allows every server owner to customize the server to their liking and create advanced setups.
- Highly Configurable so you can control every aspect of your Avatar Universe.
- Modular Abilities
  - The plugin comes with over 40 abilities pre-installed and passive abilities. Each of them can be enabled or disabled in the config file. Additionally, the ProjectKorra API allows developers to hook into the API to create new abilities and place them in the Abilities folder, making for easy installation of new abilities.
- Custom Chat Formatting / Colors based on your element.

## API

There are several ways Developers can hook into the ProjectKorra API. Developers looking to learn how to use our API, a tutorial on creating your own abilities can be found [here](https://github.com/ProjectKorra/ProjectKorra/wiki/Creating-an-Addon-Ability). Additionally, You can view the [Javadocs](http://projectkorra.com/docs) for more detailed information.

## Configuration

The ProjectKorra (Core) configuration will have its own wiki page explaining all that you need to know. (Link Coming Soon)

## Commands / Permissions

An extensive list of the available commands and permissions can be found on the wiki by clicking [here](https://github.com/ProjectKorra/ProjectKorra/wiki/Commands)

## Bending

Players may choose any of the disciplines below and play. Each discipline has its own page dedicated to it, below is just a brief summary of what each element is capable of doing.

### [Airbending](https://github.com/ProjectKorra/ProjectKorra/wiki/Airbending)

- Specializes in mobility and defense.
- The land is an ideal spot for an airbender, despite being able to bend in water as well.
- Techniques range from AirScooter, AirShield, creating Tornadoes, Blasting opponents with Air, Suffocation, and more.
- Natural pacifists, lack much raw damage output by default, but make up for it in their mobility, utility, and speed.
- Increases speed, decreasing rate of hunger, and takes no fall damage.
- Comes with the Flight and SpiritualProjection subelements.

### [Waterbending](https://github.com/ProjectKorra/ProjectKorra/wiki/Waterbending)

- Specializes in maneuverability in and on the water.
- Oceans are perfect for Waterbenders.
- By default, techniques range from freezing over lakes, manipulating water to do damage, creating waves and torrents, and much more.
- Mixture of defensive and offensive abilities.
- Opens up possibilities in bodies of water that are otherwise closed to the other disciplines.
- Comes with Bloodbending, Healing, Icebending and Plantbending subelements.

### [Earthbending](https://github.com/ProjectKorra/ProjectKorra/wiki/Earthbending)

- Specializes in manipulating the earth around them.
- Any area containing land is perfect for an earthbender.
- Techniques range from using Earth to guard yourself (Armor and Walls), using Earth to launch yourself, digging, blasting earth at other places, and much more.
- Fundamentally the same as Waterbenders, with a mix of offensive and defensive playstyles.
- Comes with the Sand, Metal and Lavabending subelements.

### [Firebending](https://github.com/ProjectKorra/ProjectKorra/wiki/Firebending)

- Very offensive playstyle.
- Any environment other than water is suitable for Firebenders.
- By default, abilities range from extinguishing fires, creating rings of fire on the gruond, creating walls of fire, lightning, shooting fire blasts, and much more.
- Very little mobility and defense.
- Comes with the Lightning and Combustion subelements.

### [Chiblocking](https://github.com/ProjectKorra/ProjectKorra/wiki/Chiblocking)

- Specializes in bare handed combat.
- Can block a user's chi, temporarily disabling their bending.
- Takes less fall damage, jump higher, and run faster.
- Strikes deal more damage than normal.
- Not very good when it comes to defense.
